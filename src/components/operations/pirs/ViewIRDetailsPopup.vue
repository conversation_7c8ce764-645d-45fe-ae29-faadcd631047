<template>
    <v-dialog
        v-model="isOpen"
        max-width="900"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card class="ma-3 bg-white border border-solid border-grey-lighten-4 rounded-lg overflow-visible">
            <v-card-title class="d-flex justify-space-between">
                <h3>IR Details</h3>
                <v-btn
                    class="ma-2"
                    variant="flat"
                    icon
                    density="compact"
                    @click="closePopup"
                >
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>
            <v-card-text>
                <v-container fluid class="pa-0 text-left">
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>IR Number:</strong>
                                <div>{{ irDetails.pirNumber }}.{{ irDetails.irNumber }}</div>
                            </div>
                        </v-col>
                        <v-col cols="12" md="8">
                            <div class="mb-2">
                                <strong>Information Requirement:</strong>
                                <div>{{ irDetails.informationRequirement }}</div>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>Priority:</strong>
                                <LumioPriorityChip :priority="irDetails.priority" :fullWidth="false" />
                            </div>
                        </v-col>
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>Status:</strong>
                                <LumioStatusChip :status="irDetails.isAnswered ? 'answered' : 'pending'" :fullWidth="false" />
                            </div>
                        </v-col>
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>Originator:</strong>
                                <div>{{ getOriginatorLabel(irDetails.originator) }}</div>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>LTIOV Date:</strong>
                                <div>{{ formatDate(irDetails.ltiovDate) }}</div>
                            </div>
                        </v-col>
                        <v-col cols="12" md="8">
                            <div class="mb-2">
                                <strong>Description:</strong>
                                <div style="white-space: pre-line;">{{ irDetails.description }}</div>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="6">
                            <div class="mb-2">
                                <strong>Created At:</strong>
                                <div>{{ formatDate(irDetails.createdAt) }}</div>
                            </div>
                        </v-col>
                        <v-col cols="12" md="6">
                            <div class="mb-2">
                                <strong>Updated At:</strong>
                                <div>{{ formatDate(irDetails.updatedAt) }}</div>
                            </div>
                        </v-col>
                    </v-row>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, watch } from 'vue';
import { IPir } from '@/types/Pir';
import { IOriginator } from '@/types/Originator.type';
import dayjs from 'dayjs';
import LumioPriorityChip from '@/components/elements/LumioPriorityChip.vue';
import LumioStatusChip from '@/components/elements/LumioStatusChip.vue';

const emit = defineEmits(['close-popup']);
const props = defineProps<{
    ir: any;
    pir: IPir;
    originators: IOriginator[];
    modelValue: boolean;
}>();

const isOpen = ref(props.modelValue);
watch(() => props.modelValue, (val) => { isOpen.value = val; });
watch(isOpen, (val) => { if (!val) emit('close-popup'); });

const irDetails = ref<any>({
    id: '',
    irNumber: '',
    informationRequirement: '',
    priority: '',
    originator: '',
    description: '',
    isAnswered: false,
    ltiovDate: '',
    createdAt: '',
    updatedAt: '',
});

function closePopup() {
    isOpen.value = false;
}

function getOriginatorLabel(originatorId: string) {
    const found = props.originators.find(o => o.id === originatorId);
    return found ? found.title : originatorId;
}

function formatDate(date: string | undefined) {
    return date ? dayjs(date).format('MMM D, YYYY HH:mm') : 'N/A';
}

onMounted(() => {
    if (props.ir) {
        irDetails.value = { 
            ...props.ir,
            pirNumber: props.pir.pirNumber
        };
    }
});
</script> 