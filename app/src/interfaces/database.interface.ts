// Define border type enum
import {LineString, Point, Polygon, Position} from "geojson";

export enum BorderType {
	SOLID = 'solid',
	DOTTED = 'dotted',
	DASHED = 'dashed',
	NONE = 'none'
}

export type UserType = 'admin' | 'user' | 'org_admin' | 'manager' | 'vendor';

export type RGBAColor = [number, number, number, number]

// Define geometry types
export type GeometryType = 'Point' | 'Polygon' | 'LineString';

export enum EGeometryType {
	POINT = 'Point',
	LINE_STRING = 'LineString',
	POLYGON = 'Polygon'
}
// Define supported geometry union type
export type SupportedGeometry = Point | Polygon | LineString;

interface RuleConfig {
	title: string;
	rules: IAccessRules;
}

export interface IAccessRules {
	delete?: string[];
	create?: string[];
	update?: string[];
	list?: string[];
	read?: string[];
	[key: string]: string[] | undefined;
}

export interface IRequestingUser {
		id: number;
		email: string;
		accountType: string;
		defaultRules: Record<string, RuleConfig>
		roles: Array<{
			id: number;
			staffDesignation: string | null;
			roleName: string;
			isCommandRole: boolean;
			isStaffRole: boolean;
			organizationId: number | null;
			isActive: boolean;
		}>;

}

export interface ITimezoneType {
	name: string;
	offset: number;
}

export enum OperationType {
	CONVENTIONAL = 'conventional',
	UNCONVENTIONAL = 'unconventional',
	HYBRID = 'hybrid',
	HADR = 'hadr'
}

export enum UserOpsAccessType {
	MANAGE = 'manage',
	UPDATE = 'update',
	READ = 'read',
	DELETE = 'delete',
	CREATE = 'create',
}

export enum AccessType {
	LIST = 'list',
	MANAGE = 'manage',
	DELETE = 'delete',
	CREATE = 'create',
	UPDATE = 'update',
	READ = 'read',
}

export enum AllowedPlatformType {
	SPACE = 'space',
	AIR = 'air',
	LAND = 'land',
	SEA = 'sea',
	HYBRID = 'hybrid',
	OTHER = 'other'
}

export enum AssetStatus {
	ACTIVE = 'active',
	INACTIVE = 'inactive',
	ENGAGED = 'engaged',
	REQUESTED = 'requested',
	PENDING_APPROVAL = 'pending_approval',
	WITHDRAWN = 'withdrawn',
	CANCELLED = 'cancelled',
	REJECTED = 'rejected'
}

export enum MissionStatus {
	PENDING_APPROVAL = 'pending_approval',
	PLANNED = 'planned',
	ACTIVE = 'active',
	INACTIVE = 'inactive',
	COMPLETED = 'completed',
	FAILED = 'failed',
	ON_HOLD = 'on_hold',
	CANCELLED = 'cancelled',
	REJECTED = 'rejected',
	WITHDRAWN = 'withdrawn',
	DELAYED = 'delayed',
	SUSPENDED = 'suspended',
}

export enum Priority {
	HIGHEST = "highest",
	HIGH = "high",
	MEDIUM = "medium",
	LOW = "low",
	NONE = "none"
}

export enum AvailabilityStatus {
	AVAILABLE = 'available',
	UNAVAILABLE = 'unavailable',
	BUSY = 'busy',
	MAINTENANCE = 'maintenance',
	OTHER = 'other'
}

export enum ApprovalStatus {
	PENDING = 'pending',
	APPROVED = 'approved',
	REJECTED = 'rejected'
}

export enum RFIStatus {
	CREATED = 'created',
	IN_PROGRESS = 'in_progress',
	REJECTED = 'rejected',
	RESOLVED = 'resolved',
	ANSWERED = 'answered',
	UNRESOLVED = 'unresolved',
}

export enum ISRStatus {
	CREATED = 'created',
	ACTIVE = 'active',
	INACTIVE = 'inactive',
	COMPLETED = 'completed',
	ON_HOLD = 'on_hold',
}

export enum CACHE_DURATIONS {
	QUICK= 30000,    // 30 seconds
	NORMAL= 60000,   // 1 minute
	LONG= 300000,    // 5 minutes
	VERY_LONG= 3600000 // 1 hour
};

export enum TaskStatus {
	CREATED = 'created',
	ACTIVE = 'active',
	HOLD = 'hold',
	PENDING = 'pending',
	BLOCKED = 'blocked',
	COMPLETED = 'completed'
}
