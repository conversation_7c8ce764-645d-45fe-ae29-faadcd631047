<template>
	<v-fade-transition>
		<tr v-if="!alreadyAssigned" :class="alreadyAssigned ? 'already-assigned font-weight-bold' : ''">
		<td class="">
			{{ user.firstName }} {{ user.lastName }}
		</td>
		<td>
			{{ user.email }}
		</td>
		<td>
			<v-chip
				v-for="role in user.roles"
				:key="role.id"
				density="compact"
				border="1"
				color="white"
				class="bg-primary text-white ma-2"
			>{{ role.roleName }}</v-chip>
		</td>
		<td>
			<div class="d-inline-flex">

				<template v-if="alreadyAssigned">
					<div class="pa-4">
						<v-icon size="30" color="primary" >mdi-check</v-icon> Already In
					</div>
				</template>
				<template v-else>
					<v-select
						v-model="accessType"
						:items="['read', 'create', 'update']"
						label="Access type"
						hide-details
						density="compact"
						width="300"
						:class="'bg-white ' + getColorBackgroundByAccessType(accessType)"
					/>
					<v-btn
						class="text-none btn btn-sm ma-2"
						color="primary"
						text="Edit"
						variant="outlined"
						@click="handleAssignUser(user.id.toString())"
					>
						<v-icon>mdi-plus</v-icon> Assign
					</v-btn>
				</template>
			</div>


		</td>
	</tr>
	</v-fade-transition>
</template>


<script setup lang="ts">
import {  User } from '@/types/User';
import { UserOperation} from '@/types/UserOperation';
import { computed, defineEmits } from 'vue';

const props = defineProps<{
	user: User;
	existingOperationUsers: UserOperation[];
}>();

const alreadyAssigned = computed(() => {
	return props.existingOperationUsers.some(
		(opUser) => {
			const oU = opUser as UserOperation;
			const oUserId = oU.user?.id as string;
			return (oUserId?.toString() === props.user.id.toString());
		}
	);
});

const emit = defineEmits(['assign-user']);

const handleAssignUser = (userId: string) => {
	// Map the UI selection to the backend value before emitting
	emit('assign-user', userId, accessType.value);
};



const accessType = ref<string>('read');

const getColorBackgroundByAccessType = (accessType: string) => {
	const colors:{[key: string]: string} = {
		read: 'bg-white',
		write: 'bg-lime-lighten-5',
		manage: 'bg-orange-lighten-4',
		list: 'bg-grey',
		update: 'bg-lime-lighten-5', // Map 'update' to same color as 'write'
	};
	return colors[accessType] || 'bg-white';
};

watch(() => props.existingOperationUsers, (o, n) => {

}, {
	deep: true
});

</script>
