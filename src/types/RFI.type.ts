import { IFullUserOperation } from '@/types/UserOperation';
import { IOriginator } from '@/types/Originator.type';
import { IPagination, Priority, RFIStatus } from '@/types/Global.type';

export interface IRFI {
	id: string | number,
	title: string,
	ltiovDate: string
	originatorId?: string | number,
	originator?: IOriginator | null,
	originatorLabel?: string,
	operationId?: string | number,
	operation?: IFullUserOperation,
	priority?: Priority,
    justification?: string | null,
	checkSource?: boolean,
	createdAt?: string,
	updatedAt?: string,
	status?: RFIStatus,
}


export interface IRFICollectionData {
	rfis: IRFI[];
	pagination: IPagination;
}

export interface IRFISingleData {
	rfi: IRFI;
}

export interface IRFIFilters {
	[key: string]: string | number | undefined; // Allow additional parameters
}