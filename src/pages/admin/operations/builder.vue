<template>
    <div class="ma-4">
        <v-card class="lumio-card">
            <v-card-title>
                <h4>Operations Builder</h4>
                <div class="d-flex justify-space-between mt-4">
                    <v-btn
                        color="primary"
                        variant="text"
                        :disabled="currentStep === 0"
                        @click="handleBack"
                        class="mr-2"
                        prepend-icon="mdi-arrow-left"
                    >
                        Back
                    </v-btn>
                    <v-btn
                        color="primary"
                        v-if="currentStep !== 6"
                        @click="handleNext"
                        append-icon="mdi-arrow-right"
                    >
                        Proceed To Next Step
                    </v-btn>
                    <v-btn
                        v-if="currentStep === 6"
                        color="primary"
                        :disabled="!step1Valid"
                        @click="handleNext"
                        prepend-icon="mdi-check"
                    >
                        Create Operation
                    </v-btn>
                </div>
            </v-card-title>
            <v-card-text>
                <v-stepper
                    v-model="currentStep as number"
                    alt-labels
                    hide-actions
                    flat
                    elevation="0"
                    editable
                    clickable
                >
                    <v-stepper-header>
                        <v-stepper-item title="Information"
                            :rules="[() => operationState.name.length > 0]" 
                            :value="0"
                            complete
                        >
                            <template v-slot:subtitle>Name & Type</template>
                        </v-stepper-item>

                        <v-divider></v-divider>

                        <v-stepper-item title="Area of Operation" :value="1">
                            <template v-slot:subtitle> Location </template>
                        </v-stepper-item>

                        <v-divider></v-divider>

                        <v-stepper-item title="Tactical Areay & Timezone"
                            :value="2"
                        >
                            <template v-slot:subtitle>
                                Area of responsibility
                            </template>
                        </v-stepper-item>

                        <v-divider></v-divider>

                        <v-stepper-item title="PIRs" :value="3">
                            <template v-slot:subtitle> Select PIRs </template>
                        </v-stepper-item>

                        <v-divider></v-divider>

                        <v-stepper-item title="Boards" :value="4">
                            <template v-slot:subtitle> Select Boards </template>
                        </v-stepper-item>

                        <v-divider></v-divider>

                        <v-stepper-item title="Data Sources" :value="5">
                            <template v-slot:subtitle>
                                Select Data Sources
                            </template>
                        </v-stepper-item>

                        <v-divider></v-divider>

                        <v-stepper-item title="Plugins" :value="6">
                            <template v-slot:subtitle>
                                Select Plugins
                            </template>
                        </v-stepper-item>
                    </v-stepper-header>

                    <v-stepper-window v-model="currentStep as number">
                        <v-stepper-window-item :value="0">
                            <Step1NameType @update="handleStep1Update" />
                        </v-stepper-window-item>

                        <v-stepper-window-item :value="1">
                            <Step2AreaOfOperation
                                :current-step="currentStep"
                                :area-of-operation="
                                    operationState.areaOfOperation
                                "
                                :location-of-operation="
                                    operationState.locationCoordinates
                                "
                                :zoom="operationState.zoom"
                                @update-area-of-ops="handleStep2Update"
                            />
                        </v-stepper-window-item>

                        <v-stepper-window-item :value="2">
                            <Step3AreaOfResponsibilityTimezone
                                :current-step="currentStep"
                                :area-of-responsibility="
                                    operationState.areaOfOperation
                                "
                                @update="handleStep3Update"
                            />
                        </v-stepper-window-item>

                        <v-stepper-window-item :value="3">
                            <Step4Pirs
                                :pirs="operationState.pirs"
                                @updatePirs="handlePirUpdates"
                            />
                        </v-stepper-window-item>

                        <v-stepper-window-item :value="4">
                            <Step5Boards @update="handleStep5Update" />
                        </v-stepper-window-item>

                        <v-stepper-window-item :value="5">
                            <Step6DataSources @update="handleStep6Update" />
                        </v-stepper-window-item>

                        <v-stepper-window-item :value="6">
                            <Step7Plugins @update="handleStep7Update" />
                        </v-stepper-window-item>
                    </v-stepper-window>
                </v-stepper>
            </v-card-text>
        </v-card>

        <div class=""></div>
    </div>
</template>

<script setup lang="ts">
import { useOperationStore } from '@/stores/operation.store';
import { ref, reactive } from 'vue';
import type { Step1FormData } from '@/types/operationBuilder/Step1FormData';
import { IArea, ILocation, type MapElementData } from '@/types/Global.type';
import Step1NameType from './steps/step1NameType.vue';
import Step2AreaOfOperation from './steps/step2AreaOfOperation.vue';
import Step3AreaOfResponsibilityTimezone from './steps/step3AreaOfResponsibilityTimezone.vue';
import Step4Pirs from './steps/step4Pirs.vue';
import Step5Boards from './steps/step5Boards.vue';
import Step6DataSources from './steps/step6DataSources.vue';
import Step7Plugins from './steps/step7Plugins.vue';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminStore } from '@/stores/admin';
import { useRouter } from 'vue-router';
import { IPir } from '@/types/Pir';

const currentStep = ref(0);
const operationStore = useOperationStore();
const router = useRouter();
// const { user } = storeToRefs(useAuthStore());

const loading = ref(false);
const { showSnackbar } = useSnackbar();
const admin = useAdminStore();

// interface ISimplePIR {
//     question: string;
//     description?: string;
//     isActive?: boolean;
// 	priority: Priority;
// 	originator?: string;
// }

interface OperationState {
    name: string;
    type: string;
    designation: string;
    description: string;
    location: string;
    areaOfOperation: IArea;
    zoom: number;
    locationCoordinates: ILocation;
    pirs: IPir[];
    boards: {
        mainBoards: string[];
        current23: string[];
        plans25: string[];
        RCMBoards: string[];
        other: string[];
    };
    dataSources: {
        geoData: string[];
        weatherData: string[];
        threatData: string[];
    };
    plugins: {
        socialMedia: string[];
        isr: string[];
        network: string[];
    };
}

interface SelectedItems {
    mainBoards: string[];
    current23: string[];
    plans25: string[];
    RCMBoards: string[];
    other: string[];
}

interface DataSourcesState {
    geoData: string[];
    weatherData: string[];
    threatData: string[];
}

interface PluginsState {
    socialMedia: string[];
    isr: string[];
    network: string[];
}

const operationState = reactive<OperationState>({
    name: '',
    type: '',
    designation: '',
    description: '',
    location: '',
    areaOfOperation: {
        type: 'Polygon',
        coordinates: [],
    },
    zoom: 0,
    locationCoordinates: {
        type: 'Point',
        coordinates: [0, 0],
    },
    pirs: [],
    boards: {
        mainBoards: [],
        current23: [],
        plans25: [],
        RCMBoards: [],
        other: [],
    },
    dataSources: {
        geoData: [],
        weatherData: [],
        threatData: [],
    },
    plugins: {
        socialMedia: [],
        isr: [],
        network: [],
    },
});

const handleStep1Update = (data: Step1FormData) => {
    operationState.name = data.name;
    operationState.type = data.type;
    operationState.designation = data.designation;
    operationState.description = data.description;
    operationState.location = data.location;
};

const handleStep2Update = (data: any) => {
    try {
        operationState.areaOfOperation = data.areaOfOperation;
        operationState.locationCoordinates = data.locationOfOperation;
        operationState.zoom = data.zoom;
    } catch (error) {}
};

const addOperation = async () => {
    try {
        const pirsCreated = operationState.pirs;
        loading.value = true;
        const formData = {
            name: operationState.name,
            locationCoordinates: operationState.locationCoordinates,
            areaOfOperation: {
                type: operationState.areaOfOperation.type,
                coordinates: [operationState.areaOfOperation.coordinates],
            },
            zoom: operationState.zoom,
            designation: operationState.designation,
            description: operationState.description,
            type: operationState.type,
            location: operationState.location,
            pirs: pirsCreated,
        };

        const response = await admin.addOperation(formData);
        const operation = response.operation;
        const operationId = operation.id;
        showSnackbar({
            text: 'Operation added successfully',
            color: 'success',
        });

        //set current operation to new operation
        if (operationId) {
            await operationStore.setOperationCurrent(operationId, operation);
        }
        localStorage.removeItem('pirsCreated');
        await router.push('/admin/operations');
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Error adding operation',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const handleStep3Update = (data: MapElementData) => {};

const handlePirUpdates = (data: IPir[]) => {
    operationState.pirs = data;
};

const handleStep5Update = (data: SelectedItems) => {
    operationState.boards = data;
};

const handleStep6Update = (data: DataSourcesState) => {
    operationState.dataSources = data;
};

const handleStep7Update = (data: PluginsState) => {
    operationState.plugins = data;
};

const step1Valid = computed(() => {
    return (
        operationState.name.length > 0 &&
        operationState.type.length > 0 &&
        operationState.designation.length > 0 &&
        operationState.description.length > 0
    );
});

const handleNext = async () => {
    if (currentStep.value < 6) {
        currentStep.value++;
    } else {
        await addOperation();
    }
};

const handleBack = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
    }
};
</script>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>
