// src/interfaces/rules/rules.operation.interface.ts

import {AccessType, ITimezoneType, OperationType} from "@/interfaces/database.interface.js";
import {Point, Polygon} from 'geojson';


export interface Operation{
	id: string | number;
	name: string;
	location: Point;
	designation: string;
	description: string;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface UserAccess {
	id: number;
	accessType: string;
}

// Point coordinates schema for validation
const pointSchema = {
	type: 'object',
	properties: {
		type: { type: 'string', enum: ['Point'] },
		coordinates: {
			type: 'array',
			minItems: 2,
			maxItems: 2,
			items: { type: 'number' }
		}
	},
	required: ['type', 'coordinates']
};

const polygonSchema = {
	type: 'object',
	required: ['type', 'coordinates'],
	properties: {
		type: { type: 'string', enum: ['Polygon'] },
		coordinates: {
			type: 'array',
			items: {
				type: 'array',
				items: {
					type: 'array',
					items: { type: 'number' },
					minItems: 2,
					maxItems: 2
				}
			}
		}
	}
};

export interface AddUpdateUsersToOperationDTO {
	users: UserAccess[];
}

export const addUpdateUsersAccessSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	Body: {
		type: 'object',
		properties: {
			users: {
				type: 'array',
				items: {
					type: 'object',
					properties: {
						id: { type: 'number' },
						accessType: { type: 'string', enum: Object.values(AccessType) }
					}
				}
			},
		},
		required: ['users']
	}
};

export interface GetOpSyncDTO {
	type?: string;
	currentTimestamp?: string;
	endDate?: string;
}

export const getOpSyncSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' },
		},
		required: [ 'id']
	},
	querystring: {
		type: 'object',
		properties: {
			type: {type: 'string'},
			endDate: {
				type: 'string',
			},
			currentTimestamp: {
				type: 'string',
			}
		},
		required: ['type']
	}
};

export interface RemoveUsersFromOperationDTO {
	userIds: number[];
}

export const removeUserAccessSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	Body: {
		type: 'object',
		properties: {
			userIds: {
				type: 'array',
				items: { type: 'number' }
			},
		},
		required: ['userIds']
	}
};

export interface UpdateOperationAccessDTO {
	users: UserAccess[];
}

export const updateOperationAccessSchema = {
	params: {
		id: { type: 'number' },
	},
	Body: {
		type: 'object',
		properties: {
			users: {
				type: 'array',
				items: {
					type: 'object',
					properties: {
						id: { type: 'number' },
						accessType: { type: 'string', enum: Object.values(AccessType) }
					}
				}
			}
		},
		required: ['users']
	}
};

interface SimplePirInterface {
	question: string;
	isActive?: boolean;
}

export interface CreateOperationDTO {
	name: string;
	config: any;
	location?: string;
	countryCode?: string;
	locationCoordinates?: Point;
	zoom?: number;
	description: string;
	tacticalAreaOfResponsibility: Polygon | null;
	//extract values from OperationType
	type: OperationType.CONVENTIONAL | OperationType.UNCONVENTIONAL | OperationType.HYBRID | OperationType.HADR;
	organizationIds?: number[];
	pirs?: SimplePirInterface[];
}

export interface UpdateOperationDTO {
	name?: string;
	location?: string;
	config?: any;
	locationCoordinates?: Point | null;
	zoom?: number;
	type: OperationType.CONVENTIONAL | OperationType.UNCONVENTIONAL | OperationType.HYBRID | OperationType.HADR;
	description?: string;
	areaOfOperation?: Polygon;
	tacticalAreaOfResponsibility?: Polygon;
	timezones?: ITimezoneType[];
	isActive?: boolean;
}


export interface ToggleOperationStatusDTO {
	isActive: boolean;
}

export interface SearchOperationsDTO {
	searchTerm: string;
}

export const searchOperationsSchema = {
	querystring: {
		type: 'object',
		properties: {
			searchTerm: { type: 'string', minLength: 1, maxLength: 100 }
		}
	}
};

export const toggleOperationStatusSchema = {
	body: {
		type: 'object',
		required: ['isActive'],
		properties: {
			isActive: { type: 'boolean' }
		}
	}
};

export const createOperationSchema = {
	body: {
		type: 'object',
		required: ['name', 'location', 'description','type'],
		properties: {
			config: { type: 'object' },
			name: { type: 'string', minLength: 1, maxLength: 255 },
			location: { type: 'string', minLength: 1, maxLength: 1000 },
			zoom: { type: 'number' },
			locationCoordinates: pointSchema,
			designation: { type: 'string', minLength: 1, maxLength: 255 },
			description: { type: 'string', minLength: 1, maxLength: 1200 },
			type: { type: 'string', enum: Object.values(OperationType) },
			organizationIds: { type: 'array', items: { type: 'number' } }
		}
	}
};

export const updateOperationSchema = {
	body: {
		type: 'object',
		properties: {
			config: { type: 'object' },
			isActive: { type: 'boolean' },
			type: { type: 'string', enum: Object.values(OperationType) },
			name: { type: 'string', minLength: 1, maxLength: 255 },
			timezones: { type: 'array', items: { type: 'object', properties: { name: { type: 'string' }, offset: { type: 'number' } } } },
			zoom: { type: 'number' },
			location: { type: 'string', minLength: 1, maxLength: 1000 },
			locationCoordinates: {
				anyOf: [
					pointSchema,
					{ type: 'null' }
				]
			},
			designation: { type: 'string', minLength: 1, maxLength: 255 },
			description: { type: 'string', minLength: 1, maxLength: 1200 },
			areaOfOperation: polygonSchema,
			tacticalAreaOfResponsibility: polygonSchema
		}
	}
};

export const getOperationUsersSchema = {
	params: {
		type: 'object',
		properties: {
			id: {type: 'string'}
		},
		required: ['id']
	}
}