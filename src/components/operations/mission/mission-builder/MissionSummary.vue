<template>
    <v-card class="lumio-card" style="height: 100%">
        <v-card-title class="py-2 bg-primary mb-6">
            <h4>Mission Summary</h4>
            <v-btn
                color="secondary"
                append-icon="mdi-arrow-right"
                @click="addToWaitList"
            >
                Go To Waitlist
            </v-btn>
        </v-card-title>
        <v-card-text class="h-100">
            <template v-if="isLoading">
                <v-skeleton-loader
                    class="mx-auto"
                    type="card"
                    :loading="isLoading"
                >
                </v-skeleton-loader>
            </template>
            <template v-else>
                <v-row v-if="mission" class="mb-4 h-100">
                    <v-col cols="5">
                        <v-card
                            class="mb-4 px-3"
                            style="background-color: #f0f0f0"
                        >
                            <v-card-title class="px-0 py-2 border-b"
                                ><b>General Information</b></v-card-title
                            >
                            <v-card-text class="pt-2">
                                <p
                                    v-if="mission.name"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Name: <b>{{ mission.name }}</b>
                                </p>
                                <p
                                    v-if="mission.classification"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Classification:
                                    <b>{{ mission.classification }}</b>
                                </p>
                                <p
                                    v-if="
                                        mission.reportTypeConfiguration
                                            .reportTypes.length
                                    "
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Report Types:
                                    <b>{{
                                        mission.reportTypeConfiguration.reportTypes.join(
                                            ', ',
                                        )
                                    }}</b>
                                </p>

                                <p
                                    v-if="mission.description"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Description:
                                    <b>{{ mission.description }}</b>
                                </p>

                                <p
                                    v-if="mission.priority"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Priority:

                                    <LumioPriorityChip
                                        :priority="mission.priority"
                                        :full-width="false"
                                    />
                                </p>

                                <p
                                    v-if="mission.startAt"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Mission Commence:
                                    <b>{{
                                        dayjs(mission.startAt).format(
                                            'MMM-D, YYYY  HH:mm',
                                        )
                                    }}</b>
                                </p>
                                <p
                                    v-if="mission.endAt"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Mission Conclude:
                                    <b>{{
                                        dayjs(mission.endAt).format(
                                            'MMM-D, YYYY  HH:mm',
                                        )
                                    }}</b>
                                </p>
                                <p
                                    v-if="mission.endAt"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Requested By User(ID):
                                    <b>{{ mission.requestedByUserId }}</b>
                                </p>
                            </v-card-text>
                        </v-card>

                        <v-card
                            class="mb-4 px-3"
                            style="background-color: #f0f0f0"
                            v-if="
                                (mission.assets && mission.assets.length > 0) ||
                                (mission.tais && mission.tais.length > 0)
                            "
                        >
                            <v-card-title class="px-0 py-2 border-b"
                                ><b>ISR Assets, TAIs</b></v-card-title
                            >
                            <v-card-text class="pt-2">
                                <p
                                    v-if="
                                        mission.assets &&
                                        mission.assets.length > 0
                                    "
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    ISR Assets:
                                    <b>{{
                                        mission.assets
                                            .map((asset) => asset.name)
                                            .join(', ')
                                    }}</b>
                                </p>

                                <p
                                    v-if="
                                        mission.tais && mission.tais.length > 0
                                    "
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    TAIs:
                                    <b>{{
                                        mission.tais
                                            .map((tai) => tai.name)
                                            .join(', ')
                                    }}</b>
                                </p>
                            </v-card-text>
                        </v-card>

                        <p
                            v-if="fetchedPirs.length > 0"
                            style="
                                font-size: 1.2rem;
                                margin-bottom: 15px;
                                margin-top: 15px;
                            "
                        >
                            <b
                                style="
                                    font-size: 1.2rem;
                                    margin-bottom: 15px;
                                    display: block;
                                "
                                >PIRs, IRs:</b
                            >
                            <!-- PIR Expansion Panels -->
                            <v-expansion-panels multiple class="elevation-1">
                                <v-expansion-panel
                                    v-for="(pir, index) in fetchedPirs"
                                    :key="pir.id"
                                    class="mb-3"
                                    style="
                                        background-color: #f8f8f8;
                                        border: 1px solid #e0e0e0;
                                        border-radius: 8px;
                                        overflow: hidden;
                                    "
                                    :opened="isPanelOpen(index)"
                                >
                                    <v-expansion-panel-title
                                        @click="togglePanel(index)"
                                        class="bg-grey-lighten-4"
                                        style="
                                            border-radius: 8px 8px 0 0;
                                            padding: 2px 10px;
                                        "
                                    >
                                        <div class="d-flex align-center">
                                            <v-icon
                                                :icon="
                                                    isPanelOpen(index)
                                                        ? 'mdi-chevron-down'
                                                        : 'mdi-chevron-right'
                                                "
                                                class="mr-2"
                                                color="primary"
                                            ></v-icon>
                                            <span
                                                class="font-weight-bold"
                                                style="font-size: 1.1rem"
                                            >
                                                {{ pir.question }}
                                                <span
                                                    class="text-grey-darken-1"
                                                    style="
                                                        font-size: 0.85rem;
                                                        color: #666;
                                                    "
                                                >
                                                    (PIR #{{ pir.id }})
                                                </span>
                                            </span>
                                        </div>
                                    </v-expansion-panel-title>
                                    <v-expansion-panel-text class="pa-1">
                                        <div class="px-0 py-0 text-body-1">
                                            {{ pir.description }}
                                        </div>

                                        <!-- Related IRs -->
                                        <div
                                            v-if="getRelatedIRs(pir).length > 0"
                                            class="mt-3"
                                        >
                                            <div
                                                class="text-subtitle-1 text-primary font-weight-medium mb-2"
                                            >
                                                Related Information
                                                Requirements:
                                            </div>

                                            <v-expansion-panels
                                                multiple
                                                class="elevation-0 ml-4"
                                            >
                                                <v-expansion-panel
                                                    v-for="(
                                                        ir, irIndex
                                                    ) in getRelatedIRs(pir)"
                                                    :key="ir.id"
                                                    class="mb-2 py-1"
                                                    style="
                                                        background-color: #f0f4f8;
                                                        border: 1px solid
                                                            #d0d9e6;
                                                        border-radius: 6px;
                                                    "
                                                    :opened="
                                                        isNestedPanelOpen(
                                                            pir.id,
                                                            irIndex,
                                                        )
                                                    "
                                                >
                                                    <v-expansion-panel-title
                                                        @click="
                                                            toggleNestedPanel(
                                                                pir.id,
                                                                irIndex,
                                                            )
                                                        "
                                                        class="bg-blue-lighten-5"
                                                        style="
                                                            border-radius: 6px
                                                                6px 0 0;
                                                        "
                                                    >
                                                        <div
                                                            class="d-flex align-center"
                                                        >
                                                            <v-icon
                                                                :icon="
                                                                    isNestedPanelOpen(
                                                                        pir.id,
                                                                        irIndex,
                                                                    )
                                                                        ? 'mdi-chevron-down'
                                                                        : 'mdi-chevron-right'
                                                                "
                                                                size="small"
                                                                class="mr-2"
                                                                color="blue-darken-1"
                                                            ></v-icon>
                                                            <span
                                                                class="font-weight-medium"
                                                                style="
                                                                    font-size: 0.95rem;
                                                                    color: #444;
                                                                "
                                                            >
                                                                Designation:
                                                                {{
                                                                    ir.designation
                                                                }}
                                                            </span>
                                                        </div>
                                                    </v-expansion-panel-title>

                                                    <v-expansion-panel-text
                                                        style="
                                                            padding: 0 10px 0
                                                                10px;
                                                        "
                                                    >
                                                        <div
                                                            class="text-body-2"
                                                        >
                                                            {{
                                                                ir.informationRequirement
                                                            }}
                                                        </div>
                                                        <div
                                                            class="text-caption mt-2 text-grey-darken-1"
                                                        >
                                                            Originator:
                                                            {{ ir.originator }}
                                                        </div>
                                                    </v-expansion-panel-text>
                                                </v-expansion-panel>
                                            </v-expansion-panels>
                                        </div>
                                    </v-expansion-panel-text>
                                </v-expansion-panel>
                            </v-expansion-panels>
                        </p>
                        <p
                            v-if="ISRTitles.length > 0"
                            style="font-size: 1.2rem; margin-bottom: 15px"
                        >
                            ISR Asset:
                            <b v-for="title in ISRTitles" :key="title">{{
                                title
                            }}</b>
                        </p>
                    </v-col>

                    <v-col cols="7" class="h-100">
                        <v-row class="">
                            <v-col cols="6">
                                <v-textarea
                                    readonly
                                    label="Indicators:"
                                    class="h-30"
                                    :value="mission.indicatorsDescription"
                                ></v-textarea>
                            </v-col>
                            <v-col cols="6">
                                <v-textarea
                                    readonly
                                    label="Warnings:"
                                    class="h-30"
                                    :value="mission.warningsDescription"
                                ></v-textarea>
                            </v-col>
                        </v-row>

                        <EsriMapViewer
                            :map-items="parseMapItems"
                            class="h-screen"
                            :operation="operation"
                            :centerCoordinates="getOperationCenter"
                            :zoom="getOperationZoom"
                        ></EsriMapViewer>
                    </v-col>
                </v-row>
            </template>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { Mission } from '@/types/Mission';
import dayjs from 'dayjs';
import { useAdminPirStore } from '@/stores/admin/pir.store';
import { useMissionStore } from '@/stores/admin/mission.store';
import { IPir } from '@/types/Pir';
import { useRouter } from 'vue-router';
import { Operation } from '@/types/Operation';
import { parseMapItemsHelper } from '@/composables/misc.helper';
import { useIsrTrackStore } from '@/stores/isr-track.store';
import { IPagination } from '@/types/Global.type';
import { IIsrTrack } from '@/types/IsrTrack.type';
import { Aoi } from '@/types/Aoi.type';
import { ref, computed, onMounted } from 'vue';
import { useEngagedMissionAssetStore } from '@/stores/admin/engaged-mission-asset.store';
import { IEngagedMissionAsset } from '@/types/EngagedMissionAsset.type';

const router = useRouter();
const missionStore = useMissionStore();
const adminPirStore = useAdminPirStore();
const isrTrackStore = useIsrTrackStore();
const fetchedPirs = ref<IPir[]>([]);
const isLoading = ref<boolean>(true);
const opsIsrTracks = ref<IIsrTrack[]>([]);

const engagedMissionAssetStore = useEngagedMissionAssetStore();
const currentEngagedAssets = ref<IEngagedMissionAsset[]>([]);

const props = defineProps<{
    mission?: Mission;
    operation: Operation;
}>();

const getEngagedAssets = async () => {
    const response = await engagedMissionAssetStore.fetchEngagedMissionAssets(
        {
            page: 1,
            perPage: 50,
            sortBy: 'desc',
            orderBy: 'id',
        } as IPagination,
        {
            missionId: (props.mission?.id as string) || '',
        },
    );
    if (!response.data) return;
    currentEngagedAssets.value = response.data?.engagedMissionAssets;
};

const genericPagiantion = {
    page: 1,
    perPage: 1000,
    pages: 1,
    sortBy: 'desc',
    orderBy: 'id',
} as IPagination;

const addToWaitList = async () => {
    await missionStore.updateMission(Number(props.mission?.id), {
        status: 'pending_approval',
    });
    await router.push(`/admin/operation/rcm?#waitlist`);
};

// const selectedAssets = ref<array[any]>([]);

const ISRTitles = computed(() => {
    return [];
});

const reports = ref<string[]>([]);

const getOperationCenter = computed(() => {
    return props.operation?.locationCoordinates?.coordinates || [0, 0];
});

const getOperationZoom = computed(() => {
    return props.operation?.zoom || 10;
});

const parseMapItems = computed(() => {
    if (!props.mission) return [];
    const tais = props.mission.tais as Aoi[];
    return parseMapItemsHelper(
        {
            aois: tais,
            engaged_assets: currentEngagedAssets.value || [],
        },
        true,
    );
});

// const parseMapItems = computed(() => {
// 	if (!currentOperation.value) return [];
// 	if (!ongoingMissions.value) return [];
// 	return parseMapItemsHelper({
// 		aois: collectedAois.value,
// 		isr_tracks: collectedIsrTracks.value,
// 		engaged_assets: collectedEngagedAssets.value,
// 	});
// });

const fetchIsrTracks = async () => {
    if (!props.operation) return;

    const apiIsrTrackResponse = (
        await isrTrackStore.fetchIsrTracks(
            genericPagiantion,
            { operationId: props.operation.id },
            ['mapElement', 'assets'],
        )
    ).data;
    if (!apiIsrTrackResponse?.isrTracks) return;
    //only fetch isr tracks that have assets
    if (props.mission && props.mission.assets) {
        const missionAssetIds = props.mission?.assets.map((asset) => asset.id);
        opsIsrTracks.value = apiIsrTrackResponse.isrTracks.filter(
            (isrTrack: IIsrTrack) => {
                //go for each isrTrack.assets and only pull those that have missionAssetIds
                if (isrTrack.assets) {
                    const isrTrackAssetIds = isrTrack.assets.map(
                        (asset) => asset.id,
                    );
                    const hasMissionAssets = isrTrackAssetIds.some((assetId) =>
                        missionAssetIds?.includes(assetId),
                    );
                    return hasMissionAssets;
                }
                return false;
            },
        );
    }
};

onMounted(async () => {
    await getEngagedAssets();
    // Get unique PIR IDs from information requirements
    const pirIds = [
        ...new Set(
            (props.mission?.informationRequirements as any[])
                .filter((ir) => ir.pirId) // Filter out IRs without pirId
                .map((ir) => ir.pirId),
        ),
    ]; // Get unique pirIds

    const parsedReports =
        typeof props.mission?.reportTypeConfiguration === 'string'
            ? JSON.parse(props.mission?.reportTypeConfiguration || '')
            : props.mission?.reportTypeConfiguration;
    reports.value = Array.isArray(parsedReports?.requiredSections)
        ? parsedReports.requiredSections
        : [];
    if (props.operation) {
        await fetchIsrTracks();
    }
    if (!pirIds || pirIds.length === 0) {
        // No PIRs to fetch
        isLoading.value = false;
        return;
    }

    // Fetch each PIR only once
    for (const pirId of pirIds) {
        const pir = await adminPirStore.fetchPirById(pirId);
        const result = pir.data?.pir;
        if (result) {
            fetchedPirs.value.push(result);
            // Initialize expansion state to false (collapsed)
            if (result.id) {
                // Initialize panel as closed
                openPanelIndices.value.delete(fetchedPirs.value.length - 1);
            }
        }
    }
    isLoading.value = false;
});

const getRelatedIRs = (pir: IPir) => {
    if (!props.mission?.informationRequirements) return [];
    return props.mission.informationRequirements.filter(
        (ir) => ir.pirId === pir.id,
    );
};

// Use a Set to track open panels by index
const openPanelIndices = ref(new Set<number>());

const isPanelOpen = (index: number): boolean => {
    return openPanelIndices.value.has(index);
};

const togglePanel = (index: number): void => {
    if (openPanelIndices.value.has(index)) {
        openPanelIndices.value.delete(index);
    } else {
        openPanelIndices.value.add(index);
    }
};

// Track nested IR panels with a Map of pirId to Set of irIndices
const openNestedPanelIndices = ref(new Map<number | string, Set<number>>());

const isNestedPanelOpen = (
    pirId: number | string | undefined,
    irIndex: number,
): boolean => {
    if (pirId === undefined) return false;
    const id = typeof pirId === 'string' ? parseInt(pirId) : pirId;
    const indices = openNestedPanelIndices.value.get(id);
    return indices ? indices.has(irIndex) : false;
};

const toggleNestedPanel = (
    pirId: number | string | undefined,
    irIndex: number,
) => {
    if (pirId === undefined) return;
    const id = typeof pirId === 'string' ? parseInt(pirId) : pirId;

    if (!openNestedPanelIndices.value.has(id)) {
        openNestedPanelIndices.value.set(id, new Set<number>());
    }

    const indices = openNestedPanelIndices.value.get(id)!;

    if (indices.has(irIndex)) {
        indices.delete(irIndex);
    } else {
        indices.add(irIndex);
    }
};
</script>
