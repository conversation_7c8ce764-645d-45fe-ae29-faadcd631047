<template>
    <v-dialog
        v-model="isOpen"
        max-width="900"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card class="ma-3 bg-white border border-solid border-grey-lighten-4 rounded-lg overflow-visible">
            <v-card-title class="d-flex justify-space-between">
                <h3>Add Information Requirement</h3>
                <v-btn
                    class="ma-2"
                    variant="flat"
                    icon
                    density="compact"
                    @click="closeIRModal"
                >
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>
            <v-card-text>
                <v-container fluid class="pa-0 text-left">
                    <v-row class="mb-2">
                        <v-col cols="12">
                            <v-textarea
                                v-model="IRFields.informationRequirement"
                                label="Information Requirement"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                                rows="3"
                            />
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <v-select
                                v-model="IRFields.originator"
                                :items="originators"
                                label="Originator"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            />
                        </v-col>
                        <v-col cols="12" md="4">
                            <v-select
                                v-model="IRFields.priority"
                                :items="priorities"
                                label="Priority"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            >
                                <template #prepend-inner>
                                    <span
                                        :style="{
                                            display: 'inline-block',
                                            width: '16px',
                                            height: '16px',
                                            'border-radius': '50%',
                                            'backgroundColor': getPriorityColor(IRFields?.priority as string),
                                            'marginRight': '8px',
                                            'border': '1px solid #ccc',
                                        }"
                                    ></span>
                                </template>
                                <template #item="{ item, props }">
                                    <v-list-item v-bind="props">
                                        <template #prepend>
                                            <span
                                                :style="{
                                                    display: 'inline-block',
                                                    width: '14px',
                                                    height: '14px',
                                                    'border-radius': '50%',
                                                    'backgroundColor': getPriorityColor(typeof item === 'string' ? item : (item.value ?? 'none')),
                                                    'marginRight': '8px',
                                                    'border': '1px solid #ccc',
                                                }"
                                            ></span>
                                        </template>
                                    </v-list-item>
                                </template>
                            </v-select>
                        </v-col>
                        <v-col cols="12" md="4">
                            <LumioDateTimePicker
                                v-model="IRFields.ltiovDate as string"
                                class="bg-white"
                                :minDateTime="new Date()"
                                placeholder="Select Date"
                                label="LTIOV Date (UTC/Zulu)"
                            ></LumioDateTimePicker>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" class="d-flex justify-end pb-6 gap-2">
                            <v-btn
                                @click="closeIRModal"
                                variant="flat"
                                color="secondary"
                                text="Cancel"
                                prepend-icon="mdi-close"
                                class="mr-2"
                                min-width="120"
                            >
                            </v-btn>
                            <v-btn
                                @click="createIR"
                                variant="flat"
                                color="primary"
                                text="Add IR"
                                prepend-icon="mdi-plus"
                                min-width="120"
                            >
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import LumioDateTimePicker from '@/components/elements/LumioDateTimePicker.vue';
import { IOriginator } from '@/types/Originator.type';
import { useAdminOriginatorStore } from '@/stores/admin/originator.store';
import { IPagination } from '@/types/Global.type';
import { getPriorityColor } from "@/composables/misc.helper";

const adminOriginatorStore = useAdminOriginatorStore();

const props = defineProps<{
    pirId: string | number | null;
    operationId: string | number | null;
}>();

const emit = defineEmits(['close-ir-modal', 'create-ir']);
const isOpen = ref(true);

const originators = ref<IOriginator[]>([]);
const priorities = ref<string[]>(['highest', 'high', 'medium', 'low', 'none']);

const IRFields = ref({
    pirId: props.pirId as string,
    originator: '',
    informationRequirement: '',
    ltiovDate: '',
    priority: 'medium',
});

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    sortBy: 'desc',
    orderBy: 'createdAt',
    pages: 1,
    total: 0,
});

const closeIRModal = () => {
    emit('close-ir-modal');
};

const createIR = () => {
    emit('create-ir', IRFields.value);
};

onMounted(async () => {
    IRFields.value.pirId = props.pirId as string;
    const response = await adminOriginatorStore.fetchOriginators(
        paginationState.value,
        {
            operationId: props.operationId as string,
        },
    );
    originators.value = response.data?.originators ?? [];
});
</script>
