<template>
    <v-card class="lumio-card">
        <v-card-title class="py-2 bg-primary mb-6">
            <h4>Mission Summary: {{ missionDetails?.name }}</h4>
        </v-card-title>
        <v-card-text>
            <template v-if="isLoading">
                <v-skeleton-loader
                    class="mx-auto"
                    type="card"
                    :loading="isLoading"
                ></v-skeleton-loader>
            </template>
            <template v-else-if="!missionDetails">
                <div class="text-center pa-4">
                    <p>No mission details available</p>
                </div>
            </template>
            <template v-else>
                <v-row v-if="missionDetails">
                    <v-col cols="5">
                        <v-card
                            class="mb-4 px-3"
                            style="background-color: #f0f0f0"
                        >
                            <v-card-title class="px-0 py-2 border-b"
                                ><b>General Information</b></v-card-title
                            >
                            <v-card-text class="pt-2">
                                <p
                                    v-if="missionDetails.classification"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Classification:
                                    <b>{{ missionDetails.classification }}</b>
                                </p>
                                <p
                                    v-if="
                                        missionDetails.reportTypeConfiguration
                                            .reportTypes.length
                                    "
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Report Types:
                                    <b>{{
                                        missionDetails.reportTypeConfiguration.reportTypes.join(
                                            ', ',
                                        )
                                    }}</b>
                                </p>

                                <p
                                    v-if="missionDetails.description"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Description:
                                    <b>{{ missionDetails.description }}</b>
                                </p>

                                <p
                                    v-if="missionDetails.priority"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Priority:
                                    <LumioPriorityChip
                                        :priority="missionDetails.priority"
                                        :full-width="false"
                                    />
                                </p>

                                <p
                                    v-if="missionDetails.startAt"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Mission Commence:
                                    <b>{{
                                        dayjs(missionDetails.startAt).format(
                                            'MMM-D, YYYY  HH:mm',
                                        )
                                    }}</b>
                                </p>
                                <p
                                    v-if="missionDetails.endAt"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Mission Conclude:
                                    <b>{{
                                        dayjs(missionDetails.endAt).format(
                                            'MMM-D, YYYY  HH:mm',
                                        )
                                    }}</b>
                                </p>
                                <p
                                    v-if="missionDetails.requestedByUserId"
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    Requested By User(ID):
                                    <b>{{
                                        missionDetails.requestedByUserId
                                    }}</b>
                                </p>
                            </v-card-text>
                        </v-card>

                        <v-card
                            class="mb-4 px-3"
                            style="background-color: #f0f0f0"
                            v-if="
                                (missionDetails.assets &&
                                    missionDetails.assets.length > 0) ||
                                (missionDetails.tais &&
                                    missionDetails.tais.length > 0)
                            "
                        >
                            <v-card-title class="px-0 py-2 border-b"
                                ><b>ISR Assets, TAIs</b></v-card-title
                            >
                            <v-card-text class="pt-2">
                                <p
                                    v-if="
                                        missionDetails.assets &&
                                        missionDetails.assets.length > 0
                                    "
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    ISR Assets:
                                    <b>{{
                                        missionDetails.assets
                                            .map((asset) => asset.name)
                                            .join(', ')
                                    }}</b>
                                </p>

                                <p
                                    v-if="
                                        missionDetails.tais &&
                                        missionDetails.tais.length > 0
                                    "
                                    style="
                                        font-size: 0.9rem;
                                        margin-bottom: 15px;
                                    "
                                >
                                    TAIs:
                                    <b>{{
                                        missionDetails.tais
                                            .map((tai) => tai.name)
                                            .join(', ')
                                    }}</b>
                                </p>
                            </v-card-text>
                        </v-card>

                        <div v-if="fetchedPirs.length > 0" class="mb-4">
                            <div class="text-h6 font-weight-bold mb-3">
                                PIRs & IRs
                            </div>
                            <v-expansion-panels multiple class="elevation-1">
                                <v-expansion-panel
                                    v-for="(pir, index) in fetchedPirs"
                                    :key="pir.id"
                                    class="mb-3"
                                    style="
                                        background-color: #f8f8f8;
                                        border: 1px solid #e0e0e0;
                                        border-radius: 8px;
                                        overflow: hidden;
                                    "
                                    :opened="isPanelOpen(index)"
                                >
                                    <v-expansion-panel-title
                                        @click="togglePanel(index)"
                                        class="bg-grey-lighten-4"
                                        style="
                                            border-radius: 8px 8px 0 0;
                                            padding: 2px 10px;
                                        "
                                    >
                                        <div class="d-flex align-center">
                                            <v-icon
                                                :icon="
                                                    isPanelOpen(index)
                                                        ? 'mdi-chevron-down'
                                                        : 'mdi-chevron-right'
                                                "
                                                class="mr-2"
                                                color="primary"
                                            ></v-icon>
                                            <span
                                                class="font-weight-bold"
                                                style="font-size: 1.1rem"
                                            >
                                                {{ pir.question }}
                                                <span
                                                    class="text-grey-darken-1"
                                                    style="
                                                        font-size: 0.85rem;
                                                        color: #666;
                                                    "
                                                >
                                                    (PIR #{{ pir.id }})
                                                </span>
                                            </span>
                                        </div>
                                    </v-expansion-panel-title>
                                    <v-expansion-panel-text class="pa-1">
                                        <div class="px-0 py-0 text-body-1">
                                            {{ pir.description }}
                                        </div>

                                        <!-- Related IRs -->
                                        <div
                                            v-if="getRelatedIRs(pir).length > 0"
                                            class="mt-3"
                                        >
                                            <div
                                                class="text-subtitle-1 text-primary font-weight-medium mb-2"
                                            >
                                                Related Information
                                                Requirements:
                                            </div>

                                            <v-expansion-panels
                                                multiple
                                                class="elevation-0 ml-4"
                                            >
                                                <v-expansion-panel
                                                    v-for="(
                                                        ir, irIndex
                                                    ) in getRelatedIRs(pir)"
                                                    :key="ir.id"
                                                    class="mb-2 py-1"
                                                    style="
                                                        background-color: #f0f4f8;
                                                        border: 1px solid
                                                            #d0d9e6;
                                                        border-radius: 6px;
                                                    "
                                                    :opened="
                                                        isNestedPanelOpen(
                                                            pir.id,
                                                            irIndex,
                                                        )
                                                    "
                                                >
                                                    <v-expansion-panel-title
                                                        @click="
                                                            toggleNestedPanel(
                                                                pir.id,
                                                                irIndex,
                                                            )
                                                        "
                                                        class="bg-blue-lighten-5"
                                                        style="
                                                            border-radius: 6px
                                                                6px 0 0;
                                                        "
                                                    >
                                                        <div
                                                            class="d-flex align-center"
                                                        >
                                                            <v-icon
                                                                :icon="
                                                                    isNestedPanelOpen(
                                                                        pir.id,
                                                                        irIndex,
                                                                    )
                                                                        ? 'mdi-chevron-down'
                                                                        : 'mdi-chevron-right'
                                                                "
                                                                size="small"
                                                                class="mr-2"
                                                                color="blue-darken-1"
                                                            ></v-icon>
                                                            <span
                                                                class="font-weight-medium"
                                                                style="
                                                                    font-size: 0.95rem;
                                                                    color: #444;
                                                                "
                                                            >
                                                                Designation:
                                                                {{
                                                                    ir.designation
                                                                }}
                                                            </span>
                                                        </div>
                                                    </v-expansion-panel-title>

                                                    <v-expansion-panel-text
                                                        style="
                                                            padding: 0 10px 0
                                                                10px;
                                                        "
                                                    >
                                                        <div
                                                            class="text-body-2"
                                                        >
                                                            {{
                                                                ir.informationRequirement
                                                            }}
                                                        </div>
                                                        <div
                                                            class="text-caption mt-2 text-grey-darken-1"
                                                        >
                                                            Originator:
                                                            {{ ir.originator }}
                                                        </div>
                                                    </v-expansion-panel-text>
                                                </v-expansion-panel>
                                            </v-expansion-panels>
                                        </div>
                                    </v-expansion-panel-text>
                                </v-expansion-panel>
                            </v-expansion-panels>
                        </div>

                        <p
                            v-if="ISRTitles.length > 0"
                            style="font-size: 1.2rem; margin-bottom: 15px"
                        >
                            ISR Asset:
                            <b v-for="title in ISRTitles" :key="title">{{
                                title
                            }}</b>
                        </p>
                    </v-col>

                    <v-col cols="7">
                        <v-row class="mt-4">
                            <v-col cols="6">
                                <p>Indicators:</p>
                                <v-textarea
                                    :value="
                                        missionDetails?.indicatorsDescription
                                    "
                                    readonly
                                ></v-textarea>
                            </v-col>
                            <v-col cols="6">
                                <p>Warnings:</p>
                                <v-textarea
                                    :value="missionDetails?.warningsDescription"
                                    readonly
                                ></v-textarea>
                            </v-col>
                        </v-row>
                        <EsriMapViewer
                            :map-items="parseMapItems"
                            :max-height="500"
                            :operation="operation"
                            :centerCoordinates="getOperationCenter"
                            :zoom="getOperationZoom"
                        ></EsriMapViewer>
                    </v-col>
                </v-row>
            </template>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { Mission } from '@/types/Mission';
import { IPir } from '@/types/Pir';
import dayjs from 'dayjs';
import { useAdminPirStore } from '@/stores/admin/pir.store';
import { parseMapItemsHelper } from '@/composables/misc.helper';
import { useIsrTrackStore } from '@/stores/isr-track.store';
import { Aoi } from '@/types/Aoi.type';
import { Operation } from '@/types/Operation';
import { IIsrTrack } from '@/types/IsrTrack.type';
import { IPagination } from '@/types/Global.type';
import { useOperationStore } from '@/stores/operation.store';
import { ref, computed, onMounted, watch } from 'vue';

const adminPirStore = useAdminPirStore();
const operationStore = useOperationStore();
const isrTrackStore = useIsrTrackStore();

const opsIsrTracks = ref<IIsrTrack[]>([]);
const reports = ref<string[]>([]);
const openPanelIndices = ref(new Set<number>());
const operation = ref<Operation | null>(null);
const fetchedPirs = ref<IPir[]>([]);
const isLoading = ref<boolean>(true);

const props = defineProps<{
    missionDetails: Mission | null;
    operationId: string | number | null;
}>();

const emit = defineEmits(['close-summary-modal', 'edit-mission']);

// const handleEditMission = () => {
//     emit('edit-mission', props.missionDetails);
//     emit('close-summary-modal');
// };

// Add debug watcher for props
watch(
    () => props,
    (newProps) => {
        console.log('Props changed:', {
            missionDetails: newProps.missionDetails,
            operationId: newProps.operationId,
        });
    },
    { deep: true, immediate: true },
);

const fetchOperation = async () => {
    if (!props.operationId) {
        console.log('No operation ID provided');
        return;
    }
    try {
        console.log('Fetching operation with ID:', props.operationId);
        const response = await operationStore.getOperationById(
            String(props.operationId),
        );
        operation.value = response.operation;
        console.log('Operation fetched:', operation.value);
    } catch (error) {
        console.error('Error fetching operation:', error);
    }
};

// Track nested IR panels with a Map of pirId to Set of irIndices
const openNestedPanelIndices = ref(new Map<number | string, Set<number>>());

const togglePanel = (index: number): void => {
    if (openPanelIndices.value.has(index)) {
        openPanelIndices.value.delete(index);
    } else {
        openPanelIndices.value.add(index);
    }
};

const isNestedPanelOpen = (
    pirId: number | string | undefined,
    irIndex: number,
): boolean => {
    if (pirId === undefined) return false;
    const id = typeof pirId === 'string' ? parseInt(pirId) : pirId;
    const indices = openNestedPanelIndices.value.get(id);
    return indices ? indices.has(irIndex) : false;
};

const ISRTitles = computed(() => {
    return [];
});

const genericPagiantion = {
    page: 1,
    perPage: 1000,
    pages: 1,
    sortBy: 'desc',
    orderBy: 'id',
} as IPagination;

const fetchIsrTracks = async () => {
    if (!operation.value || !props.missionDetails?.assets) {
        console.log('Missing operation or assets for ISR tracks');
        return;
    }

    try {
        console.log('Fetching ISR tracks for operation:', operation.value.id);
        const apiIsrTrackResponse = (
            await isrTrackStore.fetchIsrTracks(
                genericPagiantion,
                { operationId: operation.value.id },
                ['mapElement', 'assets'],
            )
        ).data;

        if (!apiIsrTrackResponse?.isrTracks) {
            console.log('No ISR tracks found');
            return;
        }

        const missionAssetIds = props.missionDetails.assets.map(
            (asset) => asset.id,
        );
        opsIsrTracks.value = apiIsrTrackResponse.isrTracks.filter(
            (isrTrack: IIsrTrack) => {
                if (isrTrack.assets) {
                    const isrTrackAssetIds = isrTrack.assets.map(
                        (asset: any) => asset.id,
                    );
                    const hasMissionAssets = isrTrackAssetIds.some((assetId) =>
                        missionAssetIds?.includes(assetId),
                    );
                    return hasMissionAssets;
                }
                return false;
            },
        );
        console.log('Filtered ISR tracks:', opsIsrTracks.value);
    } catch (error) {
        console.error('Error fetching ISR tracks:', error);
    }
};

// Update watchers to better handle PIRs
watch(
    () => props.missionDetails,
    async (newVal) => {
        console.log('Mission details changed:', newVal);
        if (newVal) {
            isLoading.value = true;
            try {
                // Add detailed logging of informationRequirements
                console.log('Information requirements structure:', {
                    value: newVal.informationRequirements,
                    type: typeof newVal.informationRequirements,
                    isArray: Array.isArray(newVal.informationRequirements),
                    length: Array.isArray(newVal.informationRequirements)
                        ? newVal.informationRequirements.length
                        : 'not an array',
                });

                // Using same synchronous approach as in onMounted
                if (newVal.informationRequirements) {
                    const informationRequirements = Array.isArray(
                        newVal.informationRequirements,
                    )
                        ? newVal.informationRequirements
                        : [];

                    const pirIds = [
                        ...new Set(
                            informationRequirements
                                .filter((ir) => ir && ir.pirId)
                                .map((ir) => ir.pirId),
                        ),
                    ];

                    if (pirIds.length > 0) {
                        fetchedPirs.value = []; // Reset existing PIRs

                        for (const pirId of pirIds) {
                            try {
                                const pir = await adminPirStore.fetchPirById(
                                    String(pirId),
                                );
                                const result = pir.data?.pir;

                                if (result) {
                                    fetchedPirs.value.push(result);
                                    openPanelIndices.value.add(
                                        fetchedPirs.value.length - 1,
                                    );

                                    if (result.id) {
                                        const id =
                                            typeof result.id === 'string'
                                                ? parseInt(result.id)
                                                : result.id;

                                        if (
                                            !openNestedPanelIndices.value.has(
                                                id,
                                            )
                                        ) {
                                            openNestedPanelIndices.value.set(
                                                id,
                                                new Set<number>(),
                                            );
                                        }

                                        const relatedIRs =
                                            informationRequirements.filter(
                                                (ir) =>
                                                    ir &&
                                                    ir.pirId === result.id,
                                            );
                                        relatedIRs.forEach((_, irIndex) => {
                                            openNestedPanelIndices.value
                                                .get(id)
                                                ?.add(irIndex);
                                        });
                                    }
                                }
                            } catch (error) {
                                console.error(
                                    `Error fetching PIR ${pirId}:`,
                                    error,
                                );
                            }
                        }
                    }
                }

                // Process report configuration
                if (newVal.reportTypeConfiguration) {
                    try {
                        const parsedReports =
                            typeof newVal.reportTypeConfiguration === 'string'
                                ? JSON.parse(
                                      newVal.reportTypeConfiguration || '',
                                  )
                                : newVal.reportTypeConfiguration;
                        reports.value = Array.isArray(
                            parsedReports?.requiredSections,
                        )
                            ? parsedReports.requiredSections
                            : [];
                        console.log(
                            'Parsed reports in watcher:',
                            reports.value,
                        );
                    } catch (error) {
                        console.error(
                            'Error parsing reports in watcher:',
                            error,
                        );
                    }
                }

                if (operation.value) {
                    await fetchIsrTracks();
                }
            } catch (error) {
                console.error('Error in missionDetails watcher:', error);
            } finally {
                isLoading.value = false;
            }
        }
    },
    { immediate: true },
);

watch(
    () => props.operationId,
    async (newVal) => {
        console.log('Operation ID changed:', newVal);
        if (newVal) {
            isLoading.value = true;
            try {
                await fetchOperation();
                if (props.missionDetails) {
                    // Using direct synchronous approach instead of fetchPirsData
                    if (props.missionDetails.informationRequirements) {
                        const informationRequirements = Array.isArray(
                            props.missionDetails.informationRequirements,
                        )
                            ? props.missionDetails.informationRequirements
                            : [];

                        const pirIds = [
                            ...new Set(
                                informationRequirements
                                    .filter((ir) => ir && ir.pirId)
                                    .map((ir) => ir.pirId),
                            ),
                        ];

                        if (pirIds.length > 0) {
                            fetchedPirs.value = []; // Reset existing PIRs

                            for (const pirId of pirIds) {
                                try {
                                    const pir =
                                        await adminPirStore.fetchPirById(
                                            String(pirId),
                                        );
                                    const result = pir.data?.pir;

                                    if (result) {
                                        fetchedPirs.value.push(result);
                                        openPanelIndices.value.add(
                                            fetchedPirs.value.length - 1,
                                        );

                                        if (result.id) {
                                            const id =
                                                typeof result.id === 'string'
                                                    ? parseInt(result.id)
                                                    : result.id;

                                            if (
                                                !openNestedPanelIndices.value.has(
                                                    id,
                                                )
                                            ) {
                                                openNestedPanelIndices.value.set(
                                                    id,
                                                    new Set<number>(),
                                                );
                                            }

                                            const relatedIRs =
                                                informationRequirements.filter(
                                                    (ir) =>
                                                        ir &&
                                                        ir.pirId === result.id,
                                                );
                                            relatedIRs.forEach((_, irIndex) => {
                                                openNestedPanelIndices.value
                                                    .get(id)
                                                    ?.add(irIndex);
                                            });
                                        }
                                    }
                                } catch (error) {
                                    console.error(
                                        `Error fetching PIR ${pirId}:`,
                                        error,
                                    );
                                }
                            }
                        }
                    }

                    await fetchIsrTracks();
                }
            } catch (error) {
                console.error('Error in operationId watcher:', error);
            } finally {
                isLoading.value = false;
            }
        }
    },
    { immediate: true },
);

// Add watcher for fetchedPirs to debug panel state
watch(
    () => fetchedPirs.value,
    (newPirs) => {
        console.log('Fetched PIRs changed:', newPirs);
        // Initialize panel states for new PIRs
        newPirs.forEach((pir, index) => {
            // Open all PIR panels by default
            openPanelIndices.value.add(index);

            // Initialize nested IR panels if there are informationRequirements
            if (props.missionDetails?.informationRequirements && pir.id) {
                const pirId =
                    typeof pir.id === 'string' ? parseInt(pir.id) : pir.id;

                // Create a set for this PIR's IRs if it doesn't exist
                if (!openNestedPanelIndices.value.has(pirId)) {
                    openNestedPanelIndices.value.set(pirId, new Set<number>());
                }

                // Get all IRs for this PIR
                const relatedIRs = getRelatedIRs(pir);

                // Open all IR panels by default
                relatedIRs.forEach((_, irIndex) => {
                    openNestedPanelIndices.value.get(pirId)?.add(irIndex);
                });

                console.log(
                    `Initialized IR panels for PIR ${pirId}:`,
                    openNestedPanelIndices.value.get(pirId),
                );
            }
        });
    },
    { immediate: true },
);

const getOperationCenter = computed(() => {
    return operation.value?.locationCoordinates?.coordinates || [0, 0];
});

const getOperationZoom = computed(() => {
    return operation.value?.zoom || 10;
});

const getRelatedIRs = (pir: IPir) => {
    if (!props.missionDetails?.informationRequirements) {
        console.log('No information requirements for PIR:', pir.id);
        return [];
    }

    // Ensure we have an array to work with
    const informationRequirements = Array.isArray(
        props.missionDetails.informationRequirements,
    )
        ? props.missionDetails.informationRequirements
        : [];

    // Direct filter like in MissionSummary
    const relatedIRs = informationRequirements.filter(
        (ir) => ir && ir.pirId === pir.id,
    );

    console.log(`Related IRs for PIR ${pir.id}:`, relatedIRs);
    return relatedIRs;
};

const toggleNestedPanel = (
    pirId: number | string | undefined,
    irIndex: number,
) => {
    if (pirId === undefined) return;
    const id = typeof pirId === 'string' ? parseInt(pirId) : pirId;

    if (!openNestedPanelIndices.value.has(id)) {
        openNestedPanelIndices.value.set(id, new Set<number>());
    }

    const indices = openNestedPanelIndices.value.get(id)!;

    if (indices.has(irIndex)) {
        indices.delete(irIndex);
    } else {
        indices.add(irIndex);
    }
};

const isPanelOpen = (index: number): boolean => {
    return openPanelIndices.value.has(index);
};

const parseMapItems = computed(() => {
    if (!props.missionDetails) return [];
    const tais = props.missionDetails.tais as Aoi[];
    return parseMapItemsHelper(
        {
            aois: tais,
            isr_tracks: opsIsrTracks.value,
        },
        true,
    );
});

// Initialize data when component mounts
onMounted(async () => {
    console.log('ViewSummaryModal mounted with props:', props);

    isLoading.value = true;
    try {
        if (props.operationId) {
            await fetchOperation();
        }

        // Direct PIR fetching approach (similar to MissionSummary)
        if (props.missionDetails?.informationRequirements) {
            console.log(
                'Getting IRs from missionDetails:',
                props.missionDetails.informationRequirements,
            );

            // Get unique PIR IDs from information requirements
            const informationRequirements = Array.isArray(
                props.missionDetails.informationRequirements,
            )
                ? props.missionDetails.informationRequirements
                : [];

            const pirIds = [
                ...new Set(
                    informationRequirements
                        .filter((ir) => ir && ir.pirId) // Filter out IRs without pirId
                        .map((ir) => ir.pirId),
                ),
            ]; // Get unique pirIds

            console.log('Unique PIR IDs to fetch:', pirIds);

            if (!pirIds || pirIds.length === 0) {
                console.log('No PIR IDs found to fetch');
            } else {
                fetchedPirs.value = []; // Reset existing PIRs

                // Fetch each PIR one by one (synchronous approach)
                for (const pirId of pirIds) {
                    try {
                        console.log(`Fetching PIR ${pirId} synchronously`);
                        const pir = await adminPirStore.fetchPirById(
                            String(pirId),
                        );
                        const result = pir.data?.pir;

                        if (result) {
                            console.log(
                                `Successfully fetched PIR ${pirId}:`,
                                result,
                            );
                            fetchedPirs.value.push(result);

                            // Initialize panel states (open by default)
                            openPanelIndices.value.add(
                                fetchedPirs.value.length - 1,
                            );

                            // Initialize IR panel states for this PIR
                            if (result.id) {
                                const id =
                                    typeof result.id === 'string'
                                        ? parseInt(result.id)
                                        : result.id;

                                // Create a set for this PIR's IRs if it doesn't exist
                                if (!openNestedPanelIndices.value.has(id)) {
                                    openNestedPanelIndices.value.set(
                                        id,
                                        new Set<number>(),
                                    );
                                }

                                // Get related IRs and open them by default
                                const relatedIRs =
                                    informationRequirements.filter(
                                        (ir) => ir && ir.pirId === result.id,
                                    );
                                relatedIRs.forEach((_, irIndex) => {
                                    openNestedPanelIndices.value
                                        .get(id)
                                        ?.add(irIndex);
                                });

                                console.log(
                                    `Initialized IR panels for PIR ${id}:`,
                                    openNestedPanelIndices.value.get(id),
                                );
                            }
                        }
                    } catch (error) {
                        console.error(`Error fetching PIR ${pirId}:`, error);
                    }
                }

                console.log('All PIRs fetched:', fetchedPirs.value);
            }
        } else {
            console.log(
                'No information requirements available in missionDetails',
            );
        }

        // Process report configuration
        if (props.missionDetails?.reportTypeConfiguration) {
            try {
                const parsedReports =
                    typeof props.missionDetails.reportTypeConfiguration ===
                    'string'
                        ? JSON.parse(
                              props.missionDetails.reportTypeConfiguration ||
                                  '',
                          )
                        : props.missionDetails.reportTypeConfiguration;
                reports.value = Array.isArray(parsedReports?.requiredSections)
                    ? parsedReports.requiredSections
                    : [];
                console.log('Parsed reports:', reports.value);
            } catch (error) {
                console.error('Error parsing reports:', error);
            }
        }

        if (operation.value && props.missionDetails?.assets) {
            await fetchIsrTracks();
        }
    } catch (error) {
        console.error('Error initializing ViewSummaryModal:', error);
    } finally {
        isLoading.value = false;
    }
});
</script>
