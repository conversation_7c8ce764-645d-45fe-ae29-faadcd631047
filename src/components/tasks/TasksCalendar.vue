<script setup lang="ts">
//COMMENT: I commented out not used code but you can comment it back in. TypeScript was complaining about it.
// @kravchenkoWeb

import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue';
import {
    format,
    startOfWeek,
    endOfWeek,
    eachDayOfInterval,
    isSameDay,
    startOfMonth,
    endOfMonth,
    isSameMonth,
    isToday,
    addWeeks,
    subWeeks,
    // parseISO,
    // startOfDay,
    // endOfDay,
} from 'date-fns';
// import { toZonedTime, fromZonedTime } from 'date-fns-tz';
import { ITask } from '@/stores/admin/task.store';
import { useRouter } from 'vue-router';
// import { useTaskStore } from '@/stores/admin/task.store';

interface Props {
    tasks: ITask[];
}

const props = defineProps<Props>();

// View state
const currentDate = ref(new Date());
const viewMode = ref<'weekly' | 'monthly'>('weekly');
const hourHeight = 60; // Height in pixels for each hour in the calendar
const workdayStartHour = 0; // Start displaying from 12 AM (midnight)
const workdayEndHour = 23; // End displaying at 11 PM (showing full 24 hours)
const showDebugInfo = ref(false); // Toggle for debug information

// Dates computation
const monthStart = computed(() => startOfMonth(currentDate.value));
const monthEnd = computed(() => endOfMonth(currentDate.value));

const calendarDays = computed(() => {
    const start = startOfWeek(monthStart.value, { weekStartsOn: 1 });
    const end = endOfWeek(monthEnd.value, { weekStartsOn: 1 });
    return eachDayOfInterval({ start, end });
});

const weekDays = computed(() => {
    const start = startOfWeek(currentDate.value, { weekStartsOn: 1 });
    const end = endOfWeek(currentDate.value, { weekStartsOn: 1 });
    return eachDayOfInterval({ start, end });
});

// Hours grid for the weekly view
const hourLabels = computed(() => {
    const hours = [];
    for (let i = workdayStartHour; i <= workdayEndHour; i++) {
        // Use 24-hour format for consistency with task positioning
        hours.push(i < 10 ? `0${i}:00` : `${i}:00`);
    }
    return hours;
});

const getTasksForDay = (day: Date) => {
    return props.tasks
        .filter((task) => {
            if (!task.dueAt) return false;
            return isSameDay(new Date(task.dueAt), day);
        })
        .sort((a, b) => {
            if (!a.dueAt || !b.dueAt) return 0;
            return new Date(a.dueAt).getTime() - new Date(b.dueAt).getTime();
        });
};

const isCurrentWeek = (day: Date) => {
    const currentWeekStart = startOfWeek(currentDate.value, {
        weekStartsOn: 1,
    });
    const currentWeekEnd = endOfWeek(currentDate.value, { weekStartsOn: 1 });
    const isCurrentMonth = isSameMonth(day, new Date());
    return isCurrentMonth && day >= currentWeekStart && day <= currentWeekEnd;
};

const navigateDate = (direction: 'prev' | 'next') => {
    if (viewMode.value === 'weekly') {
        // For weekly view, navigate by weeks
        currentDate.value =
            direction === 'next'
                ? addWeeks(currentDate.value, 1)
                : subWeeks(currentDate.value, 1);
    } else {
        const newDate = new Date(currentDate.value);
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        currentDate.value = newDate;
    }
};

const switchView = (mode: 'weekly' | 'monthly') => {
    viewMode.value = mode;
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'completed':
            return 'success';
        case 'in_progress':
            return 'primary';
        case 'pending':
            return 'warning';
        case 'cancelled':
            return 'error';
        default:
            return 'grey';
    }
};

const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
        case 'highest':
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
            return 'success';
        default:
            return 'grey';
    }
};

const getInitials = (user: { firstName: string; lastName: string }) => {
    return `${user.firstName.charAt(0)}${user.lastName.charAt(
        0,
    )}`.toUpperCase();
};

// Helper function to adjust time for timezone
const getLocalTime = (isoString: string) => {
    try {
        if (!isoString) return { hours: 0, minutes: 0 };

        // Parse the ISO string without timezone conversion
        // Format: "2023-04-26T13:00:00.000Z"
        //          0123456789012345678901
        //                    1         2
        const hours = parseInt(isoString.substring(11, 13), 10);
        const minutes = parseInt(isoString.substring(14, 16), 10);

        return { hours, minutes };
    } catch (error) {
        console.error('Error parsing date:', error);
        return { hours: 0, minutes: 0 };
    }
};

// Calculate position and height for tasks in weekly view
const getTaskPosition = (task: ITask) => {
    if (!task.dueAt) return { top: 0, height: hourHeight };

    try {
        // Get local time with timezone adjustment
        const { hours, minutes } = getLocalTime(task.dueAt);

        console.log(
            `Task: ${task.title}, Time: ${task.dueAt}, Local time: ${hours}:${minutes}`,
        );

        // Position based on local time
        let top = (hours - workdayStartHour) * hourHeight;
        top += (minutes / 60) * hourHeight;

        // Default height is at least 80px or the hour height, whichever is larger
        const height = Math.max(hourHeight, 80);

        return { top, height };
    } catch (error) {
        console.error('Error positioning task:', task.title, error);
        return { top: 0, height: hourHeight };
    }
};

// Format task time for display
const formatTaskTime = (dateString: string) => {
    if (!dateString) return '';

    try {
        // Get local time with timezone adjustment
        const { hours, minutes } = getLocalTime(dateString);

        // Format with leading zeros
        const formattedHours = hours < 10 ? `0${hours}` : `${hours}`;
        const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`;

        return `${formattedHours}:${formattedMinutes}`;
    } catch {
        return '';
    }
};

// Get task background color based on priority
const getTaskBackgroundColor = (priority: string) => {
    // Use blue as default color
    return 'rgba(var(--v-theme-primary), 0.8)';
};

// Get task border color based on priority
const getTaskBorderColor = (priority: string) => {
    // Use blue as default color
    return 'rgb(var(--v-theme-primary))';
};

// Get current date range for display in header
const currentDateRange = computed(() => {
    if (viewMode.value === 'weekly') {
        const start = weekDays.value[0];
        const end = weekDays.value[6];

        // If same month
        if (format(start, 'MMMM') === format(end, 'MMMM')) {
            return `${format(start, 'MMMM d')} - ${format(end, 'd, yyyy')}`;
        }
        // If different months
        return `${format(start, 'MMMM d')} - ${format(end, 'MMMM d, yyyy')}`;
    } else {
        return format(currentDate.value, 'MMMM yyyy');
    }
});

// Reference to the grid container for scrolling
const weekGridContainerRef = ref<HTMLElement | null>(null);

// Function to scroll to current time
const scrollToCurrentTime = () => {
    if (viewMode.value === 'weekly' && weekGridContainerRef.value) {
        const now = new Date();
        const currentHour = now.getHours();
        // Scroll to 1 hour before current time or to the start if it's early morning
        const scrollToHour = Math.max(currentHour - 1, workdayStartHour);
        const scrollPosition = (scrollToHour - workdayStartHour) * hourHeight;
        weekGridContainerRef.value.scrollTop = scrollPosition;
    }
};

// Watch for view mode changes to adjust scroll
watch(viewMode, (newMode) => {
    if (newMode === 'weekly') {
        nextTick(() => {
            scrollToCurrentTime();
        });
    }
});

// Get position for current time indicator
const getCurrentTimePosition = computed(() => {
    const now = new Date();

    // Get current local time
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    console.log(`Current time indicator: ${currentHour}:${currentMinute}`);

    // If current time is outside of displayed hours, don't show indicator
    if (currentHour < workdayStartHour || currentHour > workdayEndHour) {
        return -1;
    }

    // Calculate position based on current time
    let position = (currentHour - workdayStartHour) * hourHeight;
    position += (currentMinute / 60) * hourHeight;

    return position;
});

// Update current time position every minute
const updateCurrentTimeInterval = ref<number | null>(null);

onMounted(() => {
    // Initial scroll to current time
    nextTick(() => {
        scrollToCurrentTime();
    });

    // Set up interval to update current time indicator
    updateCurrentTimeInterval.value = window.setInterval(() => {
        // This will trigger the computed property to recalculate
        currentDate.value = new Date();
    }, 60000); // Update every minute
});

// Clean up interval when component unmounts
onUnmounted(() => {
    if (updateCurrentTimeInterval.value !== null) {
        clearInterval(updateCurrentTimeInterval.value);
    }
});

const router = useRouter();

const navigateToTask = (taskId: number) => {
    router.push(`/task/${taskId}`);
};

// Format date to a more readable format
const formatDate = (dateString: string) => {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    } catch {
        return '';
    }
};
</script>

<template>
    <div class="calendar-container lumio-card">
        <div class="calendar-header">
            <div class="calendar-header-content">
                <h4 class="calendar-title">Tasks Calendar</h4>
                <div class="view-mode-controls">
                    <v-btn
                        :color="viewMode === 'weekly' ? 'primary' : undefined"
                        variant="text"
                        size="small"
                        @click="switchView('weekly')"
                    >
                        Weekly
                    </v-btn>
                    <v-btn
                        :color="viewMode === 'monthly' ? 'primary' : undefined"
                        variant="text"
                        size="small"
                        @click="switchView('monthly')"
                    >
                        Monthly
                    </v-btn>
                </div>
                <div class="calendar-navigation">
                    <v-btn
                        icon="mdi-chevron-left"
                        variant="text"
                        size="small"
                        @click="navigateDate('prev')"
                    />
                    <h2 class="text-h6">
                        {{ currentDateRange }}
                    </h2>
                    <v-btn
                        icon="mdi-chevron-right"
                        variant="text"
                        size="small"
                        @click="navigateDate('next')"
                    />
                </div>
            </div>
        </div>

        <div class="calendar-body">
            <!-- Weekly View (Google Calendar style) -->
            <template v-if="viewMode === 'weekly'">
                <div class="weekly-view">
                    <!-- Days header -->
                    <div class="week-header">
                        <div class="hour-label-column"></div>
                        <div
                            v-for="day in weekDays"
                            :key="day.toString()"
                            class="day-column-header"
                            :class="{ 'today-column': isToday(day) }"
                        >
                            <div class="day-name">{{ format(day, 'EEE') }}</div>
                            <div
                                class="day-number"
                                :class="{ 'today-number': isToday(day) }"
                            >
                                {{ format(day, 'd') }}
                            </div>
                        </div>
                    </div>

                    <!-- Grid with hours and events -->
                    <div class="week-grid-container" ref="weekGridContainerRef">
                        <!-- Hour labels on the left -->
                        <div class="hour-labels">
                            <div
                                v-for="hour in hourLabels"
                                :key="hour"
                                class="hour-label"
                                :style="`height: ${hourHeight}px`"
                            >
                                {{ hour }}
                            </div>
                        </div>

                        <!-- Main grid -->
                        <div class="week-grid">
                            <!-- Horizontal hour lines -->
                            <div class="hour-grid-lines">
                                <div
                                    v-for="hour in hourLabels"
                                    :key="hour"
                                    class="hour-grid-line"
                                    :style="`top: ${
                                        hourLabels.indexOf(hour) * hourHeight
                                    }px`"
                                ></div>

                                <!-- Current time indicator -->
                                <div
                                    v-if="getCurrentTimePosition >= 0"
                                    class="current-time-indicator"
                                    :style="`top: ${getCurrentTimePosition}px`"
                                >
                                    <div class="current-time-dot"></div>
                                    <div class="current-time-line"></div>
                                </div>
                            </div>

                            <!-- Day columns -->
                            <div class="day-columns">
                                <div
                                    v-for="day in weekDays"
                                    :key="day.toString()"
                                    class="day-column"
                                    :class="{ 'today-column': isToday(day) }"
                                >
                                    <!-- Tasks for this day -->
                                    <div
                                        v-for="task in getTasksForDay(day)"
                                        :key="task.id"
                                        class="task-event"
                                        :style="{
                                            top: `${
                                                getTaskPosition(task).top
                                            }px`,
                                            height: `${
                                                getTaskPosition(task).height
                                            }px`,
                                            backgroundColor:
                                                getTaskBackgroundColor(
                                                    task.priority,
                                                ),
                                            borderLeft: `4px solid ${getTaskBorderColor(
                                                task.priority,
                                            )}`,
                                        }"
                                        @click="navigateToTask(task.id)"
                                    >
                                        <div class="task-event-content">
                                            <div class="task-event-title">
                                                {{ task.title }}
                                                <span
                                                    class="task-date"
                                                    v-if="
                                                        formatDate(
                                                            task.dueAt || '',
                                                        )
                                                    "
                                                >
                                                    {{
                                                        formatDate(
                                                            task.dueAt || '',
                                                        )
                                                    }}
                                                </span>
                                            </div>
                                            <div
                                                v-if="showDebugInfo"
                                                class="task-debug-info"
                                            >
                                                Date:
                                                {{ formatDate(task.dueAt || '')
                                                }}<br />
                                                Time:
                                                {{
                                                    formatTaskTime(
                                                        task.dueAt || '',
                                                    )
                                                }}
                                            </div>
                                            <div class="task-event-header">
                                                <div class="task-event-time">
                                                    {{
                                                        formatTaskTime(
                                                            task.dueAt || '',
                                                        )
                                                    }}
                                                </div>
                                                <div class="task-event-chips">
                                                    <v-chip
                                                        :color="
                                                            getStatusColor(
                                                                task.status,
                                                            )
                                                        "
                                                        size="x-small"
                                                        class="task-status-chip mr-1"
                                                    >
                                                        {{ task.status }}
                                                    </v-chip>
                                                    <v-chip
                                                        :color="
                                                            getPriorityColor(
                                                                task.priority,
                                                            )
                                                        "
                                                        size="x-small"
                                                        class="task-priority-chip"
                                                    >
                                                        {{ task.priority }}
                                                    </v-chip>
                                                </div>
                                            </div>
                                            <div
                                                v-if="task.members?.length"
                                                class="task-event-members"
                                            >
                                                <v-avatar
                                                    v-for="member in task.members.slice(
                                                        0,
                                                        3,
                                                    )"
                                                    :key="member.id"
                                                    size="20"
                                                    color="primary"
                                                    class="member-avatar"
                                                    :title="`${member.firstName} ${member.lastName}`"
                                                >
                                                    {{ getInitials(member) }}
                                                </v-avatar>
                                                <span
                                                    v-if="
                                                        task.members.length > 3
                                                    "
                                                    class="more-members"
                                                >
                                                    +{{
                                                        task.members.length - 3
                                                    }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <!-- Monthly View -->
            <template v-else>
                <div class="monthly-view">
                    <div class="weekday-headers">
                        <div
                            v-for="day in [
                                'Mon',
                                'Tue',
                                'Wed',
                                'Thu',
                                'Fri',
                                'Sat',
                                'Sun',
                            ]"
                            :key="day"
                            class="weekday-header"
                        >
                            {{ day }}
                        </div>
                    </div>
                    <div class="calendar-grid">
                        <div
                            v-for="day in calendarDays"
                            :key="day.toString()"
                            class="day-cell"
                            :class="{
                                'other-month': !isSameMonth(day, currentDate),
                                'current-week': isCurrentWeek(day),
                                today: isToday(day),
                                'month-start': isSameDay(day, monthStart),
                                'month-end': isSameDay(day, monthEnd),
                            }"
                        >
                            <div class="day-header">
                                <div
                                    class="day-number"
                                    :class="{
                                        today: isToday(day),
                                        'other-month': !isSameMonth(
                                            day,
                                            currentDate,
                                        ),
                                    }"
                                >
                                    {{ format(day, 'd') }}
                                </div>
                                <div class="day-tasks">
                                    <v-menu
                                        v-if="getTasksForDay(day).length > 0"
                                        location="top"
                                        :close-on-content-click="false"
                                    >
                                        <template v-slot:activator="{ props }">
                                            <div
                                                v-bind="props"
                                                class="task-indicator"
                                                :class="{
                                                    'has-tasks':
                                                        getTasksForDay(day)
                                                            .length > 0,
                                                }"
                                            >
                                                {{ getTasksForDay(day).length }}
                                                tasks
                                            </div>
                                        </template>
                                        <div class="task-menu-content">
                                            <v-card
                                                v-for="task in getTasksForDay(
                                                    day,
                                                )"
                                                :key="task.id"
                                                class="task-item mb-2"
                                                variant="outlined"
                                                density="compact"
                                                @click="navigateToTask(task.id)"
                                            >
                                                <v-card-item class="pa-3">
                                                    <div
                                                        class="d-flex align-center"
                                                    >
                                                        <div
                                                            class="task-time mr-3 text-body-2"
                                                        >
                                                            {{
                                                                formatTaskTime(
                                                                    task.dueAt ||
                                                                        '',
                                                                )
                                                            }}
                                                        </div>
                                                        <v-card-title
                                                            class="text-body-1 flex-grow-1"
                                                        >
                                                            {{ task.title }}
                                                        </v-card-title>
                                                    </div>
                                                    <v-card-subtitle
                                                        class="pa-0 mt-1"
                                                    >
                                                        <v-chip
                                                            :color="
                                                                getStatusColor(
                                                                    task.status,
                                                                )
                                                            "
                                                            size="small"
                                                            class="mr-1"
                                                        >
                                                            {{ task.status }}
                                                        </v-chip>
                                                        <v-chip
                                                            :color="
                                                                getPriorityColor(
                                                                    task.priority,
                                                                )
                                                            "
                                                            size="small"
                                                        >
                                                            {{ task.priority }}
                                                        </v-chip>
                                                    </v-card-subtitle>
                                                </v-card-item>
                                            </v-card>
                                        </div>
                                    </v-menu>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<style scoped>
.calendar-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.calendar-header {
    padding: 16px 24px;
    border-bottom: 1px solid
        rgba(var(--v-border-color), var(--v-border-opacity));
}

.calendar-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
}

.calendar-title {
    margin: 0;
    min-width: 120px;
    font-size: 24px;
    font-weight: 600;
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 8px;
}

.calendar-navigation h2 {
    margin: 0;
    min-width: 200px;
    text-align: center;
}

.view-mode-controls {
    display: flex;
    gap: 8px;
}

.calendar-body {
    padding: 16px;
    flex-grow: 1;
    overflow-y: auto;
    height: 600px;
}

/* Weekly view styles */
.weekly-view {
    height: calc(100% - 32px);
    display: flex;
    flex-direction: column;
}

.week-header {
    display: flex;
    border-bottom: 1px solid
        rgba(var(--v-border-color), var(--v-border-opacity));
    padding-bottom: 8px;
    margin-bottom: 8px;
}

.hour-label-column {
    width: 60px;
    min-width: 60px;
}

.day-column-header {
    flex: 1;
    text-align: center;
    padding: 8px;
}

.day-name {
    font-weight: 500;
    color: rgba(var(--v-theme-on-surface), 0.7);
}

.day-number {
    font-size: 1.5rem;
    font-weight: 500;
    margin-top: 4px;
}

.today-number {
    background-color: rgb(var(--v-theme-primary));
    color: white;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.week-grid-container {
    display: flex;
    flex-grow: 1;
    overflow-y: auto;
    height: calc(100% - 80px);
}

.hour-labels {
    width: 60px;
    min-width: 60px;
    padding-right: 8px;
    border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.hour-label {
    font-size: 0.8rem;
    color: rgba(var(--v-theme-on-surface), 0.6);
    text-align: right;
    padding-right: 8px;
    position: relative;
    top: -10px;
}

.week-grid {
    flex-grow: 1;
    position: relative;
    height: calc(24 * 60px);
    min-height: 600px;
}

.hour-grid-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hour-grid-line {
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    background-color: rgba(var(--v-border-color), var(--v-border-opacity));
}

.day-columns {
    display: flex;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
}

.day-column {
    flex: 1;
    border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    position: relative;
}

.day-column:last-child {
    border-right: none;
}

.today-column {
    background-color: rgba(var(--v-theme-primary), 0.05);
}

.task-event {
    position: absolute;
    left: 2px;
    right: 2px;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    z-index: 3;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.15s ease, box-shadow 0.15s ease;
}

.task-event:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.task-event-content {
    padding: 10px;
    display: flex;
    flex-direction: column;
    height: 90px;
}

.task-event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;
}

.task-event-time {
    font-size: 0.7rem;
    font-weight: 600;
    color: white;
}

.task-event-chips {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    max-width: 70%;
}

.task-event-title {
    font-weight: 700;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: white;
    margin-bottom: 6px;
    line-height: 1.2;
}

.task-event-members {
    display: flex;
    align-items: center;
    margin-top: auto;
}

.member-avatar {
    margin-right: 3px;
    font-size: 0.6rem;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.more-members {
    font-size: 0.7rem;
    color: white;
    font-weight: 500;
}

.task-status-chip,
.task-priority-chip {
    height: 18px !important;
    font-size: 0.7rem !important;
    font-weight: 600 !important;
    margin-bottom: 2px;
}

/* Monthly view styles */
.weekday-headers {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 8px;
}

.weekday-header {
    text-align: center;
    font-weight: 500;
    color: rgba(var(--v-theme-on-surface), 0.6);
    padding: 8px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    min-height: 600px;
}

.day-cell {
    border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    border-radius: 4px;
    padding: 4px;
    min-height: 40px;
    display: flex;
    flex-direction: column;
    position: relative;
}

.other-month {
    background-color: rgba(var(--v-theme-surface-variant), 0.05);
    opacity: 0.7;
}

.other-month::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        rgba(var(--v-border-color), 0.1),
        rgba(var(--v-border-color), 0.1) 5px,
        transparent 5px,
        transparent 10px
    );
    pointer-events: none;
}

.month-start {
    border-left: 2px solid rgb(var(--v-theme-primary));
}

.month-end {
    border-right: 2px solid rgb(var(--v-theme-primary));
}

.current-week {
    background-color: rgba(var(--v-theme-primary), 0.05);
}

.day-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

/* Style for monthly view day number */
.day-cell .day-number {
    font-size: 0.9em;
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 500;
    margin-top: 0;
}

.day-cell .day-number.other-month {
    color: rgba(var(--v-theme-on-surface), 0.4);
}

.day-cell .day-number.today {
    background-color: rgb(var(--v-theme-primary));
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.day-tasks {
    display: flex;
    align-items: center;
}

.task-indicator {
    background-color: #1976d2;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
    min-width: 60px;
}

.task-indicator:hover {
    background-color: #1565c0;
}

.task-menu-content {
    max-width: 350px;
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
    background-color: rgb(var(--v-theme-surface));
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    opacity: 1;
}

.task-menu-content .task-item {
    background-color: rgb(var(--v-theme-surface));
    border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    opacity: 1;
}

.task-menu-content .task-item:hover {
    background-color: rgba(var(--v-theme-primary), 0.05);
}

.task-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.task-item:hover {
    background-color: rgba(var(--v-theme-primary), 0.05);
}

.task-time {
    font-family: monospace;
    color: rgba(var(--v-theme-on-surface), 0.6);
    min-width: 45px;
}

.current-time-indicator {
    position: absolute;
    left: 0;
    right: 0;
    z-index: 4;
    display: flex;
    align-items: center;
}

.current-time-dot {
    position: absolute;
    left: 55px; /* Align with the start of day columns */
    width: 10px;
    height: 10px;
    background-color: red;
    border-radius: 50%;
    z-index: 5;
}

.current-time-line {
    position: absolute;
    left: 60px; /* Slightly offset from dot */
    right: 0;
    height: 2px;
    background-color: red;
    z-index: 4;
}

.task-debug-info {
    font-size: 0.6rem;
    color: white;
    opacity: 0.7;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
    max-height: 20px;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 2px 4px;
    border-radius: 2px;
}

.task-date {
    display: block;
    font-size: 0.7rem;
    font-weight: 400;
    opacity: 0.85;
    margin-top: 2px;
}
</style>
