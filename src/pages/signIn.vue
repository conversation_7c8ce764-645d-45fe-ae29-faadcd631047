<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useOperationStore } from '@/stores/operation.store';
import { useRouter } from 'vue-router';
import { useSnackbar } from '@/composables/useSnackbar';

const formData = ref({
    email: '',
    password: '',
});

const showPassword = ref(false);

const { showSnackbar } = useSnackbar();

const operationStore = useOperationStore();
const authStore = useAuthStore();
const router = useRouter();
const loading = ref(false);

const rules = {
    email: [
        (v: string) => !!v || 'Email is required',
        (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid',
    ],
    password: [(v: string) => !!v || 'Password is required'],
};

const signIn = async () => {
    try {
        loading.value = true;
        await authStore.signIn(formData.value.email, formData.value.password);
        //reset persistant operation store
        await operationStore.reset();

        
        await router.push('/home');
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to sign in',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};
</script>

<template>
    <v-container class="h-screen d-flex justify-center align-center">
        <v-row>
            <v-col cols="6 mx-auto">
                <v-card
                    class="lumio-card align-center justify-center rounded-lg"
                >
                    <v-card-title>
                        <h3>Sign In</h3>
                    </v-card-title>
                    <v-card-text class="pa-8">
                        <v-form @submit.prevent="signIn">
                            <v-text-field
                                v-model="formData.email"
                                class="mb-4"
                                label="Email"
                                :rules="rules.email"
                                required
                            />
                            <v-text-field
                                v-model="formData.password"
                                class="mb-4"
                                label="Password"
                                :type="showPassword ? 'text' : 'password'"
                                :rules="rules.password"
                                required
                            >
                                <template v-slot:append-inner>
                                    <v-icon
                                        :icon="
                                            showPassword
                                                ? 'mdi-eye-off'
                                                : 'mdi-eye'
                                        "
                                        @click="showPassword = !showPassword"
                                        style="cursor: pointer"
                                    />
                                </template>
                                <template v-slot:details>
                                    <router-link to="/forgot-password" class="text-caption text-primary"
                                        >Forgot Password?</router-link
                                    >
                                </template>
                            </v-text-field>
                            <v-row>
                            
                                <v-col class="text-right d-flex justify-space-between">
                                    <v-btn variant="flat" color="secondary" to="/register"
                                    class="mr-2"
                                        >Register Organization</v-btn
                                    >
                                    <v-btn
                                        variant="flat"
                                        color="primary"
                                        type="submit"
                                        :loading="loading"
                                        >Sign In</v-btn
                                    >
                                </v-col>
                            </v-row>
                            <v-divider class="mt-4" />
                            <v-row class="mt-4">
                                <v-col>
                                    You will be signed into your default
                                    organization. If you want to sign into a
                                    specific organization, please contact your
                                    organization's admin for
                                    organization-specific login information.
                                </v-col>
                            </v-row>
                        </v-form>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
