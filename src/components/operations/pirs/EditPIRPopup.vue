<template>
    <v-dialog
        v-model="isOpen"
        max-width="700"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card class="ma-3 bg-white border border-solid border-grey-lighten-4 rounded-lg overflow-visible">
            <v-card-title class="d-flex justify-space-between align-center">
                <h3>Edit PIR</h3>
                <v-btn
                    class="ma-2"
                    variant="flat"
                    icon
                    density="compact"
                    @click="closePopup"
                >
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>
            <v-card-text>
                <v-container fluid class="pa-0 text-left">
                    <v-row class="mb-2 align-center">
                        <v-col cols="9">
                            <v-text-field
                                v-model="pirToEdit.question"
                                label="Question"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            ></v-text-field>
                        </v-col>
                        <v-col cols="3" class="d-flex justify-end">
                            <v-switch
                                v-model="pirToEdit.isActive"
                                label="Active"
                                color="primary"
                                hide-details
                                density="compact"
                            ></v-switch>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="6">
                            <v-select
                                v-model="pirToEdit.priority"
                                label="Priority"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                                :items="Object.values(Priority)"
                            >
                            </v-select>
                        </v-col>
                        <v-col cols="12" md="6">
                            <v-select
                                density="compact"
                                v-model="pirToEdit.originator"
                                :items="originators"
                                label="Originator"
                                hide-details
                                variant="outlined"
                                color="primary"
                                base-color="primary"
                                class="bg-white"
                            />
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12">
                            <v-textarea
                                v-model="pirToEdit.description"
                                label="Description"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                                rows="3"
                            >
                            </v-textarea>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" class="d-flex justify-end pb-6 gap-2">
                            <v-btn
                                @click="closePopup"
                                variant="flat"
                                color="secondary"
                                text="Cancel"
                                prepend-icon="mdi-close"
                                class="mr-2"
                                min-width="120"
                            >
                            </v-btn>
                            <v-btn
                                @click="handleUpdatePIR"
                                variant="flat"
                                color="primary"
                                text="Update PIR"
                                prepend-icon="mdi-plus"
                                min-width="120"
                            >
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, nextTick, watch } from 'vue';
import { IPir } from '@/types/Pir';
import { IPirSingleResponse, useAdminPirStore } from '@/stores/admin/pir.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { Priority } from "@/types/Global.type";
import { IOriginator } from "@/types/Originator.type";
import { useAdminOriginatorStore } from "@/stores/admin/originator.store";

const { showSnackbar } = useSnackbar();
const adminPirStore = useAdminPirStore();
const adminOriginatorStore = useAdminOriginatorStore();

const props = defineProps<{
    pirId: string | number | null;
    operationId: string | number | null;
}>();

const emit = defineEmits(['update-pirs', 'close-update-popup']);
const isLoading = ref(true);
const isOpen = ref(true);
watch(isOpen, (val) => { if (!val) emit('close-update-popup'); });

const originators = ref<IOriginator[]>([]);

const pirToEdit = ref<IPir>({
    question: '',
    description: '',
    isActive: false,
    operationId: '',
    priority: Priority.MEDIUM,
    originator: '',
});

const paginationState = ref({
    page: 1,
    perPage: 100,
    sortBy: 'desc',
    orderBy: 'createdAt',
    pages: 1,
    total: 0,
});

async function fetchPirById() {
    const response = await adminPirStore.fetchPirById(props.pirId as string);

    if (response.success && response.data?.pir) {
        pirToEdit.value = {
            question: response.data.pir.question ?? '',
            description: response.data.pir.description ?? '',
            isActive: response.data.pir.isActive ?? false,
            operationId: response.data.pir.operationId ?? '',
            priority: response.data.pir.priority ?? Priority.MEDIUM,
            originator: response.data.pir.originator ?? '',
        };
    } else {
        showSnackbar({
            text: 'Failed to fetch PIR',
            color: 'error',
        });
    }
    isLoading.value = false;
}

async function handleUpdatePIR() {
    try {
        const response = (await adminPirStore.updatePir(
            props.pirId as string,
            pirToEdit.value,
        )) as IPirSingleResponse;
        if (response.success) {
            showSnackbar({
                text: 'PIR updated successfully',
                color: 'success',
            });
            emit('update-pirs');
            await nextTick();
            isOpen.value = false;
        } else {
            showSnackbar({
                text: 'Failed to update PIR',
                color: 'error',
            });
        }
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'An error occurred while updating PIR',
            color: 'error',
        });
    }
}

async function fetchOriginators() {
    const response = await adminOriginatorStore.fetchOriginators(
        paginationState.value,
        {
            operationId: props.operationId as string,
        }
    );
    originators.value = response.data?.originators ?? [];
}

function closePopup() {
    isOpen.value = false;
}

onMounted(async () => {
    await fetchPirById();
    await fetchOriginators();
});
</script>
