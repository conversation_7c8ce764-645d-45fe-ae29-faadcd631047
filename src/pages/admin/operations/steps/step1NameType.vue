<template>
    <v-card class="lumio-card">
        <v-card-title>
            <h4 class="">Step 1: Name and type</h4>
        </v-card-title>
        <v-card-text class="pa-2">
            <v-form>
                <v-row>
                    <v-col cols="8">
                        <v-text-field
                            v-model="formData.name"
                            label="Operation Name"
                            @input="emitChanges"
                        
                        />
                    </v-col>
    
                    <v-col cols="4">
                        <v-select
                            v-model="formData.type"
                            :items="operationTypes"
                            label="Type"
                            @change="emitChanges"
                        >
                        </v-select>
                    </v-col>
    
                </v-row>
                <v-row>
                    <v-col cols="12">
                        <v-textarea
                            v-model="formData.description"
                            label="Description"
                            placeholder="Description of the operation"
                            @input="emitChanges"
                            rows="3"
                        />
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import type { Step1FormData } from '@/types/operationBuilder/Step1FormData';

const emit = defineEmits<{
    (e: 'update', data: Step1FormData): void;
}>();

const operationTypes = ['conventional', 'unconventional', 'hybrid', 'hadr'];

const formData = reactive<Step1FormData>({
    name: '',
    type: 'conventional',
    description: '',
});

const emitChanges = () => {
    emit('update', formData);
};
</script>
