<template>
    <v-dialog
        v-model="isOpen"
        max-width="900"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card class="ma-3 bg-white border border-solid border-grey-lighten-4 rounded-lg overflow-visible">
            <v-card-title class="d-flex justify-space-between">
                <h3>Add PIR to Operation</h3>
                <v-btn
                    class="ma-2"
                    variant="flat"
                    icon
                    density="compact"
                    @click="closePopup"
                >
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>
            <v-card-text>
                <v-container fluid class="pa-0 text-left">
                    <v-row class="mb-2">
                        <v-col cols="12" md="6">
                            <v-text-field
                                v-model="pirToAdd.question"
                                label="Question"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            ></v-text-field>
                        </v-col>            
                        <v-col cols="12" md="4">
                            <v-select
                                v-model="pirToAdd.originator"
                                :items="originators"
                                label="Originator"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            />
                        </v-col>
                        <v-col cols="12" md="2" class="d-flex align-center h-100">
                            <div class="d-flex align-center h-100">
                                <v-switch
                                    v-model="pirToAdd.isActive"
                                    label="Active"
                                    color="primary"
                                    hide-details
                                    density="compact"
                                ></v-switch>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12">
                            <v-textarea
                                v-model="pirToAdd.description"
                                label="Description"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                                rows="3"
                            ></v-textarea>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" class="d-flex justify-end pb-6 gap-2">
                            <v-btn
                                @click="closePopup"
                                variant="flat"
                                color="secondary"
                                text="Cancel"
                                prepend-icon="mdi-close"
                                class="mr-2"
                                min-width="120"
                            >
                            </v-btn>
                            <v-btn
                                @click="handleAddPIR"
                                variant="flat"
                                color="primary"
                                text="Add PIR"
                                prepend-icon="mdi-plus"
                                min-width="120"
                            >
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, watch } from 'vue';
import { IPir } from '@/types/Pir';
import { useAdminPirStore } from '@/stores/admin/pir.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { Priority } from "@/types/Global.type";
import { IOriginator } from "@/types/Originator.type";
import { useAdminOriginatorStore } from "@/stores/admin/originator.store";
import { getPriorityColor } from "@/composables/misc.helper";

const { showSnackbar } = useSnackbar();
const adminPirStore = useAdminPirStore();
const adminOriginatorStore = useAdminOriginatorStore();

const props = defineProps<{
    modelValue: boolean;
    operationId: string;
}>();

const emit = defineEmits(['close-section', 'update-pirs']);
const isOpen = ref(props.modelValue);
watch(() => props.modelValue, (val) => { isOpen.value = val; });
watch(isOpen, (val) => { if (!val) emit('close-section'); });

const originators = ref<IOriginator[]>([]);
const paginationState = ref({
    page: 1,
    perPage: 100,
    sortBy: 'desc',
    orderBy: 'createdAt',
    pages: 1,
    total: 0,
});

const pirToAdd = ref<IPir>({
    question: '',
    description: '',
    isActive: true,
    operationId: props.operationId,
    priority: Priority.MEDIUM,
    originator: '',
});

async function fetchOriginators() {
    const response = await adminOriginatorStore.fetchOriginators(
        paginationState.value,
        {
            operationId: props.operationId,
        }
    );
    originators.value = response.data?.originators ?? [];
}

async function handleAddPIR() {
    try {
        const { messages, success } = await adminPirStore.createPir(pirToAdd.value);
        if (success) {
            showSnackbar({
                text: 'PIR added successfully',
                color: 'success',
            });
            pirToAdd.value = {
                question: '',
                description: '',
                isActive: true,
                operationId: props.operationId,
                priority: Priority.MEDIUM,
                originator: '',
            };
            emit('update-pirs');
            isOpen.value = false;
        } else {
            showSnackbar({
                text: messages?.[0]?.message || 'Failed to add PIR',
                color: 'error',
            });
        }
    } catch (error) {
        console.error('Error adding PIR:', error);
        showSnackbar({
            text: 'Failed to add PIR',
            color: 'error',
        });
    }
}

function closePopup() {
    isOpen.value = false;
}

onMounted(async () => {
    await fetchOriginators();
});
</script> 