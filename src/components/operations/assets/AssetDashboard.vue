<template>
    <v-card class="lumio-card">
        <v-card-title>
            <h4>Current Assets</h4>
            <div v-if="!isAddAssetsSegmentOpen">
                <v-btn
                    v-if="hasAccess(getUserRole, 'asset', 'create')"
                    color="primary"
                    prepend-icon="mdi-plus"
                    class="ma-2"
                    variant="flat"
                    @click="isAddAssetsSegmentOpen = true"
                >
                    Add Platform To Operation
                </v-btn>

                <span style="color: red; font-size: 12px" v-else>
                    Please ask org admin to add assets
                </span>
            </div>
        </v-card-title>
        <v-card-text>
            <template
                v-if="operationWithAssets && operationWithAssets.id"
                :key="getRefreshKey(operationWithAssets.id, 'platforms')"
            >
                <v-row class="" v-if="isAddAssetsSegmentOpen">
                    <v-col>
                        <add-platform-to-operation
                            @removePlatformFromOperation="
                                removePlatformFromOperation
                            "
                            :operation-id="operationWithAssets.id.toString()"
                            @close-section="isAddAssetsSegmentOpen = false"
                            :existing-assets="operationWithAssets.assets as Asset[]"
                            @update-operation="handleOperationUpdated"
                        ></add-platform-to-operation>
                    </v-col>
                </v-row>
                <v-row class="">
                    <v-col>
                        <view-manage-operation-assets
                            @removePlatformFromOperation="
                                removePlatformFromOperation
                            "
                            :key="
                                getRefreshKey(operationWithAssets.id, 'assets')
                            "
                            :operation-with-assets="operationWithAssets as Operation"
                        ></view-manage-operation-assets>
                    </v-col>
                </v-row>
            </template>
            <div v-else>
                <v-skeleton-loader
                    type="list-item-three-line"
                    v-for="i in 3"
                    :key="i"
                ></v-skeleton-loader>
            </div>
        </v-card-text>
    </v-card>
</template>
<script setup lang="ts">
import type { Asset } from '@/types/Asset';
import type { Operation } from '@/types/Operation';
import AddPlatformToOperation from '@/components/operations/assets/AddPlatformToOperation.vue';
import ViewManageOperationAssets from '@/components/operations/assets/ViewManageOperationAssets.vue';
import { useSnackbar } from '@/composables/useSnackbar';
import { useOperationStore } from '@/stores/operation.store';
import { ref, onMounted } from 'vue';
import { useRefreshManager } from '@/composables/useRefreshManager';
import { storeToRefs } from 'pinia';
import { useAdminAssetStore } from '@/stores/admin/asset.store';
import { useAccessControl } from '@/composables/useAccessControl';
import { useAuthStore } from '@/stores/auth';

const { getUserRole } = storeToRefs(useAuthStore());

const operationStore = useOperationStore();
const adminAssetStore = useAdminAssetStore();

const { hasAccess } = useAccessControl();

const { showSnackbar } = useSnackbar();

const { triggerRefresh, getRefreshKey } = useRefreshManager({
    componentName: 'Operation',
    refreshKeys: ['assets', 'platforms'] as const,
});

const { currentOperationId } = storeToRefs(operationStore);

const isAddAssetsSegmentOpen = ref(false);
const operationWithAssets = ref<Operation | null>(null);
const emits = defineEmits(['refresh-operation']);
const fetchOperationWithAssets = async () => {
    currentOperationId.value = operationStore.currentOperationId;
    const response = (
        await operationStore.getOperationById(currentOperationId.value, [
            'assets',
            'assets.platform',
        ])
    ).operation;
    operationWithAssets.value = response;
};

const removePlatformFromOperation = async (platformId: string) => {
    try {
        let results = await adminAssetStore.deleteAsset(platformId);
        if (!results.success) {
            showSnackbar({
                text: 'Error happened',
                color: 'error',
            });
            return;
        }
        await handleOperationUpdated(currentOperationId.value, 'assets');
        await handlePlatformsUpdated(currentOperationId.value, 'platforms');
        emits('refresh-operation');
        showSnackbar({
            text: 'Platform Removed from Operation',
            color: 'success',
        });
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to remove platform',
            color: 'error',
        });
    }
};

const handleOperationUpdated = async (operationId: string, key: 'assets') => {
    if (!operationId) return;

    try {
        await triggerRefresh(operationId, key, fetchOperationWithAssets);
        emits('refresh-operation');
    } catch (error) {
        console.error('Error updating operation:', error);
        showSnackbar({
            text: 'Failed to update operation',
            color: 'error',
        });
    } finally {
    }
};

const handlePlatformsUpdated = async (
    operationId: string,
    key: 'platforms',
) => {
    if (!operationId) return;

    try {
        await triggerRefresh(operationId, key, fetchOperationWithAssets);
        emits('refresh-operation');
    } catch (error) {
        console.error('Error updating operation:', error);
        showSnackbar({
            text: 'Failed to update operation',
            color: 'error',
        });
    } finally {
    }
};

onMounted(async () => {
    await fetchOperationWithAssets();
});

watch(currentOperationId, () => {
    fetchOperationWithAssets();
});
</script>
