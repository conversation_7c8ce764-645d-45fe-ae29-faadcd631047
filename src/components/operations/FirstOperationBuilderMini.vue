<template>
    <v-card flat>
        <v-card-title class="pa-2">
            <div class="d-flex justify-space-between align-center">
                <h3 class="text-h6 mb-0">Create Operation</h3>
                <v-btn
                    color="primary"
                    :disabled="!step1Valid"
                    @click="addOperation"
                    prepend-icon="mdi-check"
                >
                    Create Operation
                </v-btn>
            </div>
        </v-card-title>

        <v-tabs
            v-model="activeTab"
            class="border-b"
        >
            <v-tab 
                value="operation"
                :class="{ 'bg-white': activeTab === 'operation', 'bg-grey-lighten-2': activeTab !== 'operation' }"
            >
                Operation Details
            </v-tab>
            <v-tab 
                value="pirs"
                :class="{ 'bg-white': activeTab === 'pirs', 'bg-grey-lighten-2': activeTab !== 'pirs' }"
            >
                PIRs
            </v-tab>
        </v-tabs>

        <v-card-text class="">
            <v-tabs-window v-model="activeTab">
                <v-tabs-window-item value="operation">
                    <v-row>
                        <!-- Left Column - Form Fields -->
                        <v-col cols="5">
                            <Step1NameType @update="handleStep1Update" />
                        </v-col>

                        <!-- Right Column - Map -->
                        <v-col cols="7">
                            <Step2AreaOfOperation
                                :current-step="1"
                                :area-of-operation="operationState.areaOfOperation"
                                :location-of-operation="operationState.locationCoordinates"
                                :zoom="operationState.zoom"
                                :country-code="operationState.countryCode"
                                :location="operationState.location"
                                @update-area-of-ops="handleStep2Update"
                            />
                        </v-col>
                    </v-row>
                </v-tabs-window-item>

                <v-tabs-window-item value="pirs">
                    <Step4Pirs
                        :pirs="operationState.pirs"
                        @updatePirs="handlePirUpdates"
                    />
                </v-tabs-window-item>
            </v-tabs-window>
            <pre>{{ operationState }}</pre>
        </v-card-text>
    </v-card>
</template>

<style scoped>
.border-b {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
</style>

<script setup lang="ts">
import { useOperationStore } from '@/stores/operation.store';
import { ref, reactive, computed } from 'vue';
import type { Step1FormData } from '@/types/operationBuilder/Step1FormData';
import { IArea, ILocation, ICoordinates } from '@/types/Global.type';
import Step1NameType from '@/pages/admin/operations/steps/step1NameType.vue';
import Step2AreaOfOperation from '@/pages/admin/operations/steps/step2AreaOfOperation.vue';
import Step4Pirs from '@/pages/admin/operations/steps/step4Pirs.vue';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminStore } from '@/stores/admin';
import { useRouter } from 'vue-router';
import { IPir } from '@/types/Pir';

const activeTab = ref('operation');
const operationStore = useOperationStore();
const router = useRouter();
const loading = ref(false);
const { showSnackbar } = useSnackbar();
const admin = useAdminStore();

interface OperationState {
    name: string;
    type: string;
    designation: string;
    description: string;
    location: string;
    countryCode: string;
    areaOfOperation: IArea;
    zoom: number;
    locationCoordinates: ILocation;
    pirs: IPir[];
    config: any;
    organizationIds: string[];
}

const operationState = reactive<OperationState>({
    name: '',
    type: '',
    designation: '',
    description: '',
    location: '',
    countryCode: '',
    areaOfOperation: {
        type: 'Polygon',
        coordinates: null
    },
    zoom: 0,
    locationCoordinates: {
        type: 'Point',
        coordinates: [0, 0],
    },
    pirs: [],
    config: {},
    organizationIds: [],
});

interface Step2UpdateData {
    areaOfOperation: {
        coordinates: ICoordinates[];
    };
    locationOfOperation: ILocation;
    zoom: number;
    location: string;
    countryCode: string;
}

const handleStep1Update = (data: Step1FormData) => {
    operationState.name = data.name;
    operationState.type = data.type;
    operationState.description = data.description;
};

const handleStep2Update = (data: Step2UpdateData) => {
    try {
        operationState.areaOfOperation = {
            type: 'Polygon',
            coordinates: [data.areaOfOperation.coordinates]
        };
        operationState.locationCoordinates = data.locationOfOperation;
        operationState.zoom = data.zoom;
        operationState.location = data.location;
        operationState.countryCode = data.countryCode;
    } catch (error) {
        console.error('Error in handleStep2Update:', error);
    }
};

const handlePirUpdates = (data: IPir[]) => {
    operationState.pirs = data;
};

const step1Valid = computed(() => {
    return (
        operationState.name.length > 0 &&
        operationState.type.length > 0 &&
        operationState.location.length > 0 &&
        operationState.countryCode.length > 0 &&
        operationState.locationCoordinates.coordinates[0] !== 0 &&
        operationState.locationCoordinates.coordinates[1] !== 0
    );
});

const addOperation = async () => {
    try {
        const pirsCreated = operationState.pirs;
        loading.value = true;
        const formData = {
            name: operationState.name,
            config: operationState.config,
            location: operationState.location,
            countryCode: operationState.countryCode,
            locationCoordinates: operationState.locationCoordinates,
            zoom: operationState.zoom,
            description: operationState.description,
            areaOfOperation: operationState.areaOfOperation,
            tacticalAreaOfResponsibility: null,
            type: operationState.type,
            pirs: pirsCreated,
        };

        const response = await admin.addOperation(formData);
        const operation = response.operation;
        const operationId = operation.id;
        showSnackbar({
            text: 'Operation added successfully',
            color: 'success',
        });

        if (operationId) {
            await operationStore.setOperationCurrent(operationId, operation);
        }
        localStorage.removeItem('pirsCreated');
        await router.push('/admin/operations');
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Error adding operation',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};
</script>
