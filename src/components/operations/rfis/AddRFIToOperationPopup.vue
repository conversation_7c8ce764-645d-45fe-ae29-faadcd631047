<template>
    <v-dialog
        v-model="isOpen"
        max-width="900"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card class="ma-3 bg-white border border-solid border-grey-lighten-4 rounded-lg overflow-visible">
            <v-card-title class="d-flex justify-space-between">
                <h3>Add RFI to Operation</h3>
                <v-btn
                    class="ma-2"
                    variant="flat"
                    icon
                    density="compact"
                    @click="closePopup"
                >
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>
            <v-card-text>
                <v-container fluid class="pa-0 text-left">
                    <v-row class="mb-2">
                        <v-col cols="12" md="8">
                            <v-text-field
                                v-model="rfiToAdd.title"
                                label="Title"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                            <v-select
                                v-model="rfiToAdd.priority"
                                :items="['highest', 'high', 'medium', 'low', 'none']"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                label="Priority"
                                class="bg-white"
                            >
                                <template #prepend-inner>
                                    <span
                                        :style="{
                                            display: 'inline-block',
                                            width: '16px',
                                            height: '16px',
                                            'border-radius': '50%',
                                            'backgroundColor': getPriorityColor(rfiToAdd?.priority as string),
                                            'marginRight': '8px',
                                            'border': '1px solid #ccc',
                                        }"
                                    ></span>
                                </template>
                                <template #item="{ item, props }">
                                    <v-list-item v-bind="props">
                                        <template #prepend>
                                            <span
                                                :style="{
                                                    display: 'inline-block',
                                                    width: '14px',
                                                    height: '14px',
                                                    'border-radius': '50%',
                                                    'backgroundColor': getPriorityColor(typeof item === 'string' ? item : (item.value ?? 'none')),
                                                    'marginRight': '8px',
                                                    'border': '1px solid #ccc',
                                                }"
                                            ></span>
                                        </template>
                            
                                    </v-list-item>
                                </template>
                            </v-select>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <v-select
                                v-model="rfiToAdd.originatorId"
                                :items="originators"
                                item-value="id"
                                label="Originator"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            ></v-select>
                        </v-col>
                        <v-col cols="12" md="4">
                            <LumioDateTimePicker
                                v-model="rfiToAdd.ltiovDate as string"
                                class="bg-white"
                                :minDateTime="new Date()"
                                placeholder="Select Date"
                                label="LTIOV Date (UTC/Zulu)"
                            ></LumioDateTimePicker>
                        </v-col>
                        <v-col cols="12" md="4" class="d-flex align-center h-100">
                            <div class="d-flex align-center h-100">
                                <v-switch
                                    v-model="rfiToAdd.checkSource"
                                    label="Check Source"
                                    color="primary"
                                    hide-details
                                    density="compact"
                                    
                                ></v-switch>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12">
                            <v-textarea
                                v-model="rfiToAdd.justification"
                                label="Justification"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                                rows="3"
                            ></v-textarea>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" class="d-flex justify-end pb-6 gap-2">
                            <v-btn
                                @click="closePopup"
                                variant="flat"
                                color="secondary"
                                text="Cancel"
                                prepend-icon="mdi-close"
                                class="mr-2"
                                min-width="120"
                            >
                            </v-btn>
                            <v-btn
                                @click="handleAddRFI"
                                variant="flat"
                                color="primary"
                                text="Add RFI"
                                prepend-icon="mdi-plus"
                                min-width="120"
                            >
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { useAdminRFIStore } from '@/stores/admin/rfi.store';
import { useSnackbar } from '@/composables/useSnackbar';
import {
    IOriginatorCollectionResponse,
    useAdminOriginatorStore,
} from '@/stores/admin/originator.store';
import { defineEmits, onMounted, ref, watch } from 'vue';
import type { IRFI } from '@/types/RFI.type';
import { IPagination, Priority, RFIStatus } from '@/types/Global.type';
import { IOriginator } from '@/types/Originator.type';
import '@vuepic/vue-datepicker/dist/main.css';
import {getPriorityColor} from "@/composables/misc.helper";

const { showSnackbar } = useSnackbar();
const adminOriginatorStore = useAdminOriginatorStore();
const adminRfiStore = useAdminRFIStore();
const originatorPaginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    total: 0,
    pages: 0,
    sortBy: 'desc',
    orderBy: 'id',
});
const originators = ref<IOriginator[]>([]);
const emit = defineEmits(['close-section', 'update-rfis']);

const props = defineProps<{
    modelValue: boolean;
    operationId: string;
}>();

const isOpen = ref(props.modelValue);
watch(() => props.modelValue, (val) => { isOpen.value = val; });
watch(isOpen, (val) => { if (!val) emit('close-section'); });

const rfiToAdd = ref<Omit<IRFI, 'id'>>({
    title: '',
    ltiovDate: '',
    originatorId: '',
    originatorLabel: '',
    priority: Priority.NONE,
    operationId: props.operationId as string,
    checkSource: false,
    status: RFIStatus.CREATED,
    justification: '',
});

const fetchOriginators = async () => {
    const { data, success, messages } =
        (await adminOriginatorStore.fetchOriginators(
            originatorPaginationState.value,
            {
                operationId: props.operationId,
            },
        )) as IOriginatorCollectionResponse;
    if (!success) {
        showSnackbar({
            text: messages[0].message as string,
            color: 'error',
        });
    }
    originators.value = data?.originators as IOriginator[];
};

const handleAddRFI = async () => {
    try {
        rfiToAdd.value.originatorLabel = originators.value.find(
            (o) => o.id === rfiToAdd.value.originatorId,
        )?.title;
        rfiToAdd.value.operationId = props.operationId;
        const { messages, success } = await adminRfiStore.createRFI(
            rfiToAdd.value as IRFI,
        );
        if (success) {
            showSnackbar({
                text: 'RFI added successfully',
                color: 'success',
            });
            rfiToAdd.value = {
                title: '',
                ltiovDate: '',
                originatorId: '',
                originatorLabel: '',
                priority: Priority.NONE,
                operationId: props.operationId as string,
                checkSource: false,
                status: RFIStatus.CREATED,
                justification: '',
            };
            emit('update-rfis');
            isOpen.value = false;
        } else {
            showSnackbar({
                pos: 'top-center',
                text: 'Failed to add RFI',
                color: 'error',
            });
            showSnackbar({
                pos: 'top-center',
                text: messages[0].message as string,
                color: 'error',
            });
        }
    } catch (error) {
        console.error(error);
    }
};

function closePopup() {
    isOpen.value = false;
}

onMounted(async () => {
    await fetchOriginators();
});
</script>
