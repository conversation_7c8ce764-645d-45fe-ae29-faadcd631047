<template>
    <div class="pa-2">
        <h2 class="mb-4">Welcome To DeepHelm</h2>
        <v-stepper :items="['Create Operation', 'Add Users', 'Add Platform']">
            <template v-slot:item.1>
                <FirstOperationBuilderMini />
            </template>

            <template v-slot:item.2>
                <v-card title="Step Two" flat>...</v-card>
            </template>

            <template v-slot:item.3>
                <v-card title="Step Three" flat>...</v-card>
            </template>
        </v-stepper>
        <p>
            step1: setup operation (Operation Builder)
        </p>
        <p>
            step2: add users to operation
        </p>
        <p>
            step3: add platforms to operation
        </p>
    </div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
</route>

<script setup lang="ts">


</script>
