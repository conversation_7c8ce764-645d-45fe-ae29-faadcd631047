/**
 * main.ts
 *
 * Bootstraps Vuetify and other plugins then mounts the App`
 */

// Shepherd.js
import 'shepherd.js/dist/css/shepherd.css';

// Plugins
import { registerPlugins } from '@/plugins';

// components
import App from './App.vue';

// Composables
import { createApp } from 'vue';

const app = createApp(App);

import 'flag-icons/css/flag-icons.min.css';
registerPlugins(app);

app.mount('#app');
