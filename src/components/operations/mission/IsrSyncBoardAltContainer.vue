<template>
    <v-card class="lumio-card h-100">
        <v-card-title>
            <h4>ISR Sync</h4>
            <div v-if="isPanelLoading">
                <v-skeleton-loader
                    class="mx-auto"
                    :loading="isPanelLoading"
                    :height="50"
                    width="100%"
                    transition="fade-transition"
                ></v-skeleton-loader>
            </div>
            <div class="my-2 d-flex" v-else>
                <div
                    class="d-flex justify-center align-center"
                    v-for="period in [24, 48, 72]"
                >
                    <v-btn
                        color="primary"
                        class="mr-2 h-100"
                        size="large"
                        density="comfortable"
                        :variant="
                            currentPredifinedTimeFrame === period
                                ? 'flat'
                                : 'outlined'
                        "
                        @click="setRange(period)"
                        :key="period"
                    >
                        {{ period }}
                    </v-btn>
                </div>

                <LumioDateTimeRangePicker
                    :periods="predefinedPeriods"
                    :startDateTime="isrSyncStartDate.toString()"
                    :endDateTime="isrSyncEndDate.toString()"
                    @range-changed="handleRangeChanged"
                    :startToday="false"
                />
            </div>
        </v-card-title>
        <v-card-text class="">
            <v-row class="pb-2">
                <v-col cols="12" md="4" class="py-0">
                    <v-card class="mx-3 mb-2 h-100" variant="outlined">
                        <v-card-subtitle
                            class="px-3 py-2 font-weight-bold border-b bg-grey-lighten-5"
                        >
                            Main Effort
                        </v-card-subtitle>
                        <v-card-text class="px-3 py-2 bg-white">
                            <em>{{ currentOperation?.config?.mainEfforts }}</em>
                        </v-card-text>
                    </v-card>
                </v-col>
                <v-col cols="12" md="4" class="py-0">
                    <v-card class="mx-3 mb-2 h-100" variant="outlined">
                        <v-card-subtitle
                            class="px-3 py-2 font-weight-bold border-b bg-grey-lighten-5"
                        >
                            Supporting Effort
                        </v-card-subtitle>
                        <v-card-text class="px-3 py-2">
                            <em>{{
                                currentOperation?.config?.supportingEfforts
                            }}</em>
                        </v-card-text>
                    </v-card>
                </v-col>
                <v-col cols="12" md="4" class="py-0">
                    <v-card class="mx-3 mb-2 h-100" variant="outlined">
                        <v-card-subtitle
                            class="px-3 py-2 font-weight-bold border-b bg-grey-lighten-5"
                        >
                            Filters
                        </v-card-subtitle>
                        <v-card-text class="px-3 py-2">
                            <div class="d-flex flex-wrap">
                                <v-switch
                                    v-model="selectedItems"
                                    density="compact"
                                    color="primary"
                                    label="NAIs"
                                    value="nais"
                                    class="mx-2"
                                    hide-details
                                ></v-switch>
                                <v-switch
                                    v-model="selectedItems"
                                    density="compact"
                                    color="primary"
                                    label="TAIs"
                                    value="tais"
                                    class="mx-2"
                                    hide-details
                                ></v-switch>
                                <v-switch
                                    v-model="selectedItems"
                                    density="compact"
                                    color="primary"
                                    label="ISR Tracks"
                                    value="isrs"
                                    class="mx-2"
                                    hide-details
                                ></v-switch>
                                <v-switch
                                    v-model="selectedItems"
                                    density="compact"
                                    color="primary"
                                    label="Missions"
                                    value="mission"
                                    class="mx-2"
                                    hide-details
                                ></v-switch>
                            </div>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
            <v-row>
                <v-col cols="12" class="py-0">
                    <div
                        v-if="isPanelLoading"
                        class="d-flex justify-center pa-5"
                    >
                        <v-progress-circular
                            :width="3"
                            :size="50"
                            color="primary"
                            indeterminate
                        ></v-progress-circular>
                    </div>
                    <div v-else class="isr-panel">
                        <v-row
                            v-if="currentOperation && currentOperation.id"
                            class="ma-0 pa-0"
                        >
                            <v-col cols="12" class="map-container ma-0 pa-0">
                                <EsriMapViewer
                                    :key="esriMApKey"
                                    v-if="
                                        currentOperation &&
                                        currentOperation.id &&
                                        isVisible
                                    "
                                    :map-items="parseMapItems"
                                    :operation="currentOperation"
                                    :centerCoordinates="getCenterCoordinates"
                                    :zoom="getOperationZoom"
                                    :max-height="500"
                                ></EsriMapViewer>
                            </v-col>
                        </v-row>
                        <v-row class="ma-0 mt-2"> </v-row>
                        <v-row class="ma-0">
                            <v-col
                                cols="12"
                                class=""
                                v-if="
                                    ongoingMissions &&
                                    ongoingMissions.length > 0
                                "
                            >
                                <template v-if="isTimeLineloading">
                                    <v-skeleton-loader
                                        class="mx-auto"
                                        :loading="isTimeLineloading"
                                        :height="50"
                                        width="100%"
                                        transition="fade-transition"
                                    ></v-skeleton-loader>
                                </template>
                                <template v-else>
                                    <LumioSyncTimeline
                                        :hours-to-show="getCustomTimeFrame"
                                        :items="getTimelineItems"
                                        :show-current-time="true"
                                        :timeline-start="isrSyncStartDate"
                                    ></LumioSyncTimeline>
                                </template>
                            </v-col>
                            <v-col v-else class="ml-2 pa-4 mb-10" cols="9">
                                <v-alert
                                    class="mx-auto pa-3 bg-secondary"
                                    border="start"
                                    :height="50"
                                >
                                    <h3 class="ml-3">No ISR Syncs Found</h3>
                                </v-alert>
                            </v-col>
                        </v-row>
                    </div>
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { Operation } from '@/types/Operation';
import { useOperationStore } from '@/stores/operation.store';
import LumioDateTimeRangePicker from '@/components/elements/LumioDateTimeRangePicker.vue';
import LumioSyncTimeline from '@/components/elements/LumioSyncTimeline.vue';
import 'vue-timeline-chart/style.css';
import {
    onMounted,
    ref,
    computed,
    watch,
    nextTick,
    onBeforeUnmount,
} from 'vue';
import { Mission } from '@/types/Mission';
import { parseMapItemsHelper } from '@/composables/misc.helper';
import { Aoi } from '@/types/Aoi.type';
import { IIsrTrack } from '@/types/IsrTrack.type';
import { TimelineItem } from '@/types/Global.type';
import { Asset } from '@/types/Asset';
import { IEngagedMissionAsset } from '@/types/EngagedMissionAsset.type';

const currentOperation = ref<Operation | null>(null);
const operationStore = useOperationStore();
// Get current date and time
const now = new Date();
const formatISODateTime = (date: Date) => date.toISOString();
const currentPredifinedTimeFrame = ref<number>(0);
const isrSyncStartDate = ref<Date>(new Date());
const isrSyncEndDate = ref<Date>(new Date(Date.now() + 24 * 60 * 60 * 1000));

const props = defineProps<{
    currentOperationId: string | number | null;
}>();
const isTimeLineloading = ref(true);
const isPanelLoading = ref(true);
const ongoingMissions = ref<any[]>([]);

const esriMApKey = ref(new Date().getTime());
const isVisible = ref(false);
let observer: IntersectionObserver | null = null;

const step = ref<number>(1);

const collectedAois = ref<Aoi[]>([]);
const collectedIsrTracks = ref<IIsrTrack[]>([]);
const collectedEngagedAssets = ref<IEngagedMissionAsset[]>([]);

const selectedItems = ref<string[]>(['tais', 'isrs', 'nais', 'mission']);

const getCurrentOperation = async () => {
    try {
        const response = await operationStore.getOperationById(
            String(props.currentOperationId),
            ['isrTracks'],
        );
        currentOperation.value = response.operation;
    } catch (error) {}
};

const getIntervals = computed(() => {
    return ['1', '4', '6', '12', '24', '48'];
});

const getOperationZoom = computed(() => {
    if (!currentOperation.value) return 10;
    return currentOperation.value.zoom;
});

const getSync = async () => {
    try {
        let response;
        const timeStamp = new Date(isrSyncStartDate.value).getTime();
        response = await operationStore.getISRSync(
            String(props.currentOperationId),
            String('custom'),
            String(timeStamp),
            String(isrSyncEndDate.value),
        );
        ongoingMissions.value = response.syncs;
    } catch (error) {}
};

const getherMissionsData = () => {
    // Reset collections to prevent duplicates when switching tabs
    collectedAois.value = [];
    collectedIsrTracks.value = [];
    collectedEngagedAssets.value = [];

    ongoingMissions.value.forEach((mission) => {
        if (mission.tais && selectedItems.value.includes('tais')) {
            mission.tais.forEach((aoi: Aoi) => {
                collectedAois.value.push(aoi);
            });
        } else {
            collectedAois.value = [];
        }
        if (mission.assets && selectedItems.value.includes('isrs')) {
            //lets do call signs
            let callSigns = [];
            mission.assets.forEach((asset: Asset) => {
                callSigns.push(asset.callSign);
                if (asset.isrTracks) {
                    asset.isrTracks.forEach((isrTrack: IIsrTrack) => {
                        let isrTrackToAdd = isrTrack;
                        //check if collectedIsrTracks already has this isrTrack
                        const existingIndex =
                            collectedIsrTracks.value.findIndex(
                                (item) => item.id === isrTrack.id,
                            );
                        if (existingIndex === -1) {
                            isrTrackToAdd.assets = [];
                            isrTrackToAdd.assets.push(asset);
                            collectedIsrTracks.value.push(isrTrackToAdd);
                        } else {
                            if (
                                !collectedIsrTracks.value[existingIndex].assets
                            ) {
                                collectedIsrTracks.value[existingIndex].assets =
                                    [];
                            }
                            collectedIsrTracks.value[existingIndex].assets.push(
                                asset,
                            );
                        }
                    });
                }
            });
        }
        if (
            mission.engagedMissionAssets &&
            mission.engagedMissionAssets.length > 0
        ) {
            mission.engagedMissionAssets.forEach(
                (engagedAsset: IEngagedMissionAsset) => {
                    collectedEngagedAssets.value.push(engagedAsset);
                },
            );
        }
    });
};

const getCenterCoordinates = computed(() => {
    if (!currentOperation.value?.locationCoordinates) return [0, 0];
    return currentOperation.value.locationCoordinates.coordinates;
});

const parseMapItems = computed(() => {
    if (!currentOperation.value) return [];
    if (!ongoingMissions.value) return [];
    return parseMapItemsHelper({
        aois: collectedAois.value,
        isr_tracks: collectedIsrTracks.value,
        engaged_assets: collectedEngagedAssets.value,
    });
});

//const timeFrame = ref(24);

const getTimelineItems = computed<TimelineItem[]>(() => {
    let isrTracks = [] as TimelineItem[];

    ongoingMissions.value.forEach((mission: Mission) => {
        if (mission.engagedMissionAssets) {
            mission.engagedMissionAssets.forEach(
                (engagedItem: IEngagedMissionAsset) => {
                    const isrTrack = engagedItem.isrTrack;
                    const asset = engagedItem.asset;

                    const isrItem = {
                        id: engagedItem?.id
                            ? engagedItem.id.toString()
                            : Date.now().toString(),
                        callSign: asset?.callSign ? asset.callSign : 'N/A',
                        assetName: asset?.title ? asset.title : 'N/A',
                        isrDesignation: isrTrack?.designation,
                        isrTrackName: isrTrack?.label,
                        label: mission.name,
                        isrTrackOtherAssets: [],
                        startDateTime: mission.startAt.toString(),
                        endDateTime: mission.endAt.toString(),
                        itemBackgroundColor: asset?.color
                            ? asset.color
                            : '#000',
                        itemLabel: `${asset?.name ? asset.name : 'None'} (${
                            asset?.callSign
                        })`,
                        priority: mission.priority,
                        segmentClasses: 'isr-track-segment',
                        rowClasses: 'bg-secondary-lighten-4',
                    } as TimelineItem;

                    isrTracks.push(isrItem);
                },
            );
        }

        // if (!mission.startAt || !mission.endAt) return;
        // console.log('mission', mission.name, mission.startAt, mission.endAt);
        // if (mission.assets) {
        // 	let assetCallSigns = [];
        // 	mission.assets.forEach((asset) => {
        //
        // 		if (asset.isrTracks) {
        // 			assetCallSigns.push(asset.callSign);
        //
        // 			asset.isrTracks.forEach((isrTrack: IIsrTrack) => {
        // 				const isrItem = {
        // 					id: isrTrack.id+"-"+mission.id+"-"+asset.id,
        // 					callSign: asset.callSign ? asset.callSign : 'N/A',
        // 					assetName: asset.title ? asset.title : 'N/A',
        // 					isrDesignation: isrTrack.designation,
        // 					isrTrackName: isrTrack.label,
        // 					label: mission.name,
        // 					isrTrackOtherAssets: assetCallSigns,
        // 					startDateTime: mission.startAt.toString(),
        // 					endDateTime: mission.endAt.toString(),
        // 					itemBackgroundColor: asset.color,
        // 					itemLabel: `${asset.name} (${asset.callSign})`,
        // 					priority: 'low',
        // 					segmentClasses: 'isr-track-segment',
        // 					rowClasses: 'bg-secondary-lighten-4',
        // 				} as TimelineItem;
        // 				//find by id if exist, update, if not push
        // 				const existingIndex = isrTracks.findIndex(
        // 					(item) => item.id === isrItem.id,
        // 				);
        // 				if (existingIndex !== -1) {
        // 					isrTracks[existingIndex] = isrItem;
        // 				} else {
        // 					isrTracks.push(isrItem);
        // 				}
        // 			});
        // 		}
        // 	});
        // }
    });

    return [...isrTracks];
});

const getCustomTimeFrame = computed(() => {
    //if current props.type === 'custom'
    if (isrSyncStartDate.value && isrSyncEndDate.value) {
        return Math.ceil(
            (isrSyncEndDate.value.getTime() -
                isrSyncStartDate.value.getTime()) /
                (1000 * 60 * 60),
        );
    }

    return 24;
});
// Function to update map key and force reinitialization
const updateMapKey = () => {
    nextTick(() => {
        esriMApKey.value = new Date().getTime();
    });
};

const setRange = async (range: number) => {
    //set range from right now to + range hours
    isPanelLoading.value = true;
    isTimeLineloading.value = true;
    isrSyncStartDate.value = new Date();
    isrSyncEndDate.value = new Date();
    isrSyncEndDate.value.setHours(isrSyncEndDate.value.getHours() + range);
    await getCurrentOperation();
    await getSync();
    await getherMissionsData();
    isPanelLoading.value = false;
    isTimeLineloading.value = false;
    updateMapKey();
    currentPredifinedTimeFrame.value = range;
};

watch(
    () => selectedItems.value,
    (newVal) => {
        if (newVal) {
            isPanelLoading.value = true;
            getherMissionsData();
            updateMapKey();
            isPanelLoading.value = false;
            isTimeLineloading.value = false;
        }
    },
);

onMounted(async () => {
    isPanelLoading.value = true;
    try {
        await getCurrentOperation();
        await getSync();
        await getherMissionsData();
        //timeFrame.value = Number(props.type);
        let intervals = getIntervals.value;
        step.value = parseInt(intervals[0]);

        // Set up visibility observer to detect when tab is visible
        observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        isVisible.value = true;
                        updateMapKey();
                    } else {
                        isVisible.value = false;
                    }
                });
            },
            { threshold: 0.1 },
        );

        // Create a slight delay to ensure DOM is ready
        setTimeout(() => {
            // Start observing the current panel
            // const panelElement = document.querySelector(
            // 	`[data-panel-id="${props.type}"]`,
            // );
            // if (observer && panelElement) {
            // 	observer.observe(panelElement);
            // }

            isVisible.value = true;
            updateMapKey();
            isPanelLoading.value = false;
        }, 300);
    } catch (error) {
        console.error('Error initializing panel:', error);
        isPanelLoading.value = false;
        isTimeLineloading.value = false;
    } finally {
        isPanelLoading.value = false;
        isTimeLineloading.value = false;
    }
    isTimeLineloading.value = false;
});

onBeforeUnmount(() => {
    // Clean up observer
    if (observer) {
        observer.disconnect();
        observer = null;
    }
});

const predefinedPeriods = [
    {
        label: 'Next 24 Hours',
        startDateTime: formatISODateTime(now),
        endDateTime: formatISODateTime(
            new Date(now.getTime() + 24 * 60 * 60 * 1000),
        ),
    },
    {
        label: 'Next 48 Hours',
        startDateTime: formatISODateTime(now),
        endDateTime: formatISODateTime(
            new Date(now.getTime() + 48 * 60 * 60 * 1000),
        ),
    },
    {
        label: 'Next 72 Hours',
        startDateTime: formatISODateTime(now),
        endDateTime: formatISODateTime(
            new Date(now.getTime() + 72 * 60 * 60 * 1000),
        ),
    },
    {
        label: 'Next 7 Days',
        startDateTime: formatISODateTime(now),
        endDateTime: formatISODateTime(
            new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000),
        ),
    },
];

onMounted(() => {
    if (props.currentOperationId) {
        getCurrentOperation();
    }
});

const handleRangeChanged = async (startDate: any, endDate: any) => {
    isPanelLoading.value = true;
    isTimeLineloading.value = true;
    isrSyncStartDate.value = new Date(startDate);
    isrSyncEndDate.value = new Date(endDate);
    await getCurrentOperation();
    await getSync();
    await getherMissionsData();
    isPanelLoading.value = false;
    isTimeLineloading.value = false;
    updateMapKey();
};
</script>
