import { Asset } from './Asset';
import { UserOperation } from '@/types/UserOperation';
import { IPoint } from '@/types/Global.type';
import {IIsrTrack} from "@/types/IsrTrack.type";
export interface Operation {
    id?: string;
    name: string;
    location: string;
    designation: string;
    description: string;
    isActive?: boolean;
    createdAt?: string;
    updatedAt?: string;
    users?: UserOperation[];
    assets?: Asset[];
    zoom?: number;
    countryCode?: string;
    locationCoordinates?: IPoint | null;
    config?: {
        mainEfforts?: string;
        supportingEfforts?: string;
    };
	isrTracks?: IIsrTrack[];
    timezones?: any[];
    areaOfOperation?: any;
    tacticalAreaOfResponsibility?: any;
}

export interface IAccessType {
    read: string;
    write: string;
    manage: string;
}
