<template>
    <v-dialog
        v-model="isOpen"
        max-width="900"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card class="ma-3 bg-white border border-solid border-grey-lighten-4 rounded-lg overflow-visible">
            <v-card-title class="d-flex justify-space-between">
                <h3>Edit RFI</h3>
                <v-btn
                    class="ma-2"
                    variant="flat"
                    icon
                    density="compact"
                    @click="closePopup"
                >
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>
            <v-card-text>
                <v-container fluid class="pa-0 text-left">
                    <v-row class="mb-2">
                        <v-col cols="12" md="6">
                            <v-text-field
                                v-model="rfiToEdit.title"
                                label="Title"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3">
                            <v-select
                                v-model="rfiToEdit.priority"
                                :items="['highest', 'high', 'medium', 'low', 'none']"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                label="Priority"
                                class="bg-white"
                            >
                                <template #prepend-inner>
                                    <span
                                        :style="{
                                            display: 'inline-block',
                                            width: '16px',
                                            height: '16px',
                                            'border-radius': '50%',
                                            'backgroundColor': getPriorityColor(rfiToEdit?.priority as string),
                                            'marginRight': '8px',
                                            'border': '1px solid #ccc',
                                        }"
                                    ></span>
                                </template>
                                <template #item="{ item, props }">
                                    <v-list-item v-bind="props">
                                        <template #prepend>
                                            <span
                                                :style="{
                                                    display: 'inline-block',
                                                    width: '14px',
                                                    height: '14px',
                                                    'border-radius': '50%',
                                                    'backgroundColor': getPriorityColor(typeof item === 'string' ? item : (item.value ?? 'none')),
                                                    'marginRight': '8px',
                                                    'border': '1px solid #ccc',
                                                }"
                                            ></span>
                                        </template>
                                    
                                    </v-list-item>
                                </template>
                            </v-select>
                        </v-col>
                        <v-col cols="12" md="3">
                            <v-select
                                v-model="rfiToEdit.status"
                                :items="['created', 'sent', 'received', 'closed']"
                                label="Status"
                                variant="outlined"
                                density="compact"
                                class="bg-white"
                                color="primary"
                                base-color="primary"
                                hide-details
                            ></v-select>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <v-select
                                v-model="rfiToEdit.originatorId"
                                :items="originators"
                                item-title="title"
                                item-value="id"
                                label="Originator"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                            ></v-select>
                        </v-col>
                        <v-col cols="12" md="4">
                            <LumioDateTimePicker
                                v-model="rfiToEdit.ltiovDate as string"
                                class="bg-white"
                                :minDateTime="new Date()"
                                placeholder="Select Date"
                                label="LTIOV Date (UTC/Zulu)"
                            ></LumioDateTimePicker>
                        </v-col>
                        <v-col cols="12" md="4" class="d-flex align-center h-100">
                            <div class="d-flex align-center h-100">
                                <v-switch
                                    v-model="rfiToEdit.checkSource"
                                    label="Check Source"
                                    color="primary"
                                    hide-details
                                    density="compact"
                                ></v-switch>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                
                        <v-col cols="12" md="12">
                            <v-textarea
                                v-model="rfiToEdit.justification"
                                label="Justification"
                                variant="outlined"
                                density="compact"
                                color="primary"
                                base-color="primary"
                                hide-details
                                class="bg-white"
                                rows="3"
                            ></v-textarea>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" class="d-flex justify-end pb-6 gap-2">
                            <v-btn
                                @click="closePopup"
                                variant="flat"
                                color="secondary"
                                text="Cancel"
                                prepend-icon="mdi-close"
                                class="mr-2"
                                min-width="120"
                            >
                            </v-btn>
                            <v-btn
                                @click="handleUpdateRFI"
                                variant="flat"
                                color="primary"
                                text="Update RFI"
                                prepend-icon="mdi-plus"
                                min-width="120"
                            >
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, nextTick, watch } from 'vue';
import { IRFISingleResponse, useAdminRFIStore } from '@/stores/admin/rfi.store';
import { IOriginatorCollectionResponse } from '@/stores/admin/originator.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { IOriginator } from '@/types/Originator.type';
import { IRFI } from '@/types/RFI.type';
import { IPagination, Priority, RFIStatus } from '@/types/Global.type';
import { useAdminOriginatorStore } from '@/stores/admin/originator.store';
import { getPriorityColor } from '@/composables/misc.helper';

const { showSnackbar } = useSnackbar();
const adminRFIStore = useAdminRFIStore();
const adminOriginatorStore = useAdminOriginatorStore();
const props = defineProps<{
    rfiId: string | number;
    operationId: string;
}>();

const emit = defineEmits(['update-rfis', 'close-update-popup']);
const isLoading = ref(true);
const isOpen = ref(true);
watch(isOpen, (val) => { if (!val) emit('close-update-popup'); });

const rfiToEdit = ref<Omit<IRFI, 'id'>>({
    title: '',
    ltiovDate: '',
    originatorId: '',
    originatorLabel: '',
    priority: Priority.NONE,
    operationId: props.rfiId as string,
    checkSource: false,
    status: RFIStatus.CREATED,
    justification: '',
});

const originatorPaginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    total: 0,
    pages: 0,
    sortBy: 'desc',
    orderBy: 'id',
});

const originators = ref<IOriginator[]>([]);

const fetchOriginators = async () => {
    const { data, success, messages } =
        (await adminOriginatorStore.fetchOriginators(
            originatorPaginationState.value,
            {
                operationId: props.operationId,
            },
        )) as IOriginatorCollectionResponse;
    if (!success) {
        showSnackbar({
            text: messages[0].message as string,
            color: 'error',
        });
    }
    originators.value = data?.originators as IOriginator[];
};

async function fetchRFIById() {
    const response = await adminRFIStore.fetchRFIById(props.rfiId);

    if (response.success && response.data?.rfi) {
        rfiToEdit.value = {
            title: response.data.rfi.title ?? '',
            ltiovDate: response.data.rfi.ltiovDate ?? '',
            originatorId: response.data.rfi.originatorId ?? '',
            originatorLabel: response.data.rfi.originatorLabel ?? '',
            priority: response.data.rfi.priority ?? Priority.NONE,
            operationId: response.data.rfi.operationId ?? '',
            checkSource: response.data.rfi.checkSource ?? false,
            status: response.data.rfi.status ?? RFIStatus.CREATED,
            justification: response.data.rfi.justification ?? '',
        };
    } else {
        showSnackbar({
            text: 'Failed to fetch Originator',
            color: 'error',
        });
    }
    isLoading.value = false;
}

async function handleUpdateRFI() {
    try {
        const response = (await adminRFIStore.updateRFI(
            props.rfiId,
            rfiToEdit.value as IRFI,
        )) as IRFISingleResponse;
        if (response.success) {
            showSnackbar({
                text: 'RFI updated successfully',
                color: 'success',
            });
            emit('update-rfis');
            await nextTick();
            isOpen.value = false;
        } else {
            showSnackbar({
                text: 'Failed to update RFI',
                color: 'error',
            });
        }
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'An error occurred while updating RFI',
            color: 'error',
        });
    }
}

function closePopup() {
    isOpen.value = false;
}

onMounted(async () => {
    await fetchRFIById();
    await fetchOriginators();
});
</script>
