<template>
	<v-table>
		<thead>
		<tr>
			<th>Designation</th>
			<th>Asset</th>
			<th class="text-center" style="width: 80px">Status</th>
			<th class="text-center">Capability</th>
			<th class="text-center">Owner</th>
			<th class="text-center">Contact</th>
			<th class="text-center">Assignment</th>
			<th class="text-center">CallSign</th>
			<th class="text-center">Color</th>
			<th></th>
		</tr>
		</thead>
		<tbody>
		<ViewManageOperationAssetsRow
			v-for="asset in operationWithAssets.assets"
			:key="asset.id"
			:asset="asset"
			@remove-asset="handleRemoveAsset"
			@change-asset-status="updateAssetStatus"
			@change-asset-color="updateAssetColor"
		/>
		</tbody>
	</v-table>

</template>

<script setup lang="ts">
import ViewManageOperationAssetsRow from '@/components/operations/assets/ViewManageOperationAssetsRow.vue';
import { Operation } from '@/types/Operation';
import { useAdminAssetStore } from '@/stores/admin/asset.store';

const assetStore = useAdminAssetStore();


const props = defineProps<{
	operationWithAssets: Operation;
}>();
const emits = defineEmits(['remove-platform-from-operation', 'operation-updated']);


const handleRemoveAsset = async (assetId:string) => {
	try {
    emits('remove-platform-from-operation', assetId);
	} catch (error) {

	}
};


// const sendUpdateEmit = () => {
// 	// emits('operation-updated', props.operationWithAssets.id);
// 	return
// };



const updateAssetStatus = async (assetId:string, status:string) => {
	try{
		await assetStore.updateAsset(assetId, {
            status: status
		});
		// emits('operation-updated', props.operationWithAssets.id);
	} catch (error) {

	}
}

const updateAssetColor = async (assetId:string, color:string) => {
	try {
		await assetStore.updateAsset(assetId, {
			color: color
		});
		emits('operation-updated', props.operationWithAssets.id);
	} catch (error) {

	}
}
</script>

