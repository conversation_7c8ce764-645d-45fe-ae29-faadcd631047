<template>
    <tr class="compact-table">
        <td class=""></td>
        <td class="text-center">{{ pir.pirNumber }}.{{ ir.irNumber }}</td>
        <td class="text-center">
            <LumioPriorityChip :priority="ir.priority" />
        </td>
        <!-- <td class="text-center">
            <LumioStatusChip v-if="ir.isAnswered" :status="`answered`" />
        </td> -->
    
        <td>
            <div class="ml-4 cursor-pointer" @click="isViewingDetails = true">{{ ir.informationRequirement }}</div>
        </td>
        <td class="text-center">
            {{ ir.originator }}
        </td>
        <td class="text-center">
            {{ dayjs(ir.ltiovDate).format('MMM-D, YYYY  HH:mm') }}
        </td>
    
        <td class="text-center nowrap-col">
            <v-btn
                variant="flat"
                color="primary"
                size="small"
                class="ml-2"
                @click="showIrEditPopup"
            >
                <v-icon>mdi-pencil</v-icon>
            </v-btn>
            <v-btn
                variant="flat"
                color="error"
                size="small"
                class="ml-2"
                @click="deleteIR(ir.id)"
            >
                <v-icon>mdi-delete</v-icon>
            </v-btn>
        </td>
    </tr>
    <ViewIRDetailsPopup
        v-if="isViewingDetails"
        v-model="isViewingDetails"
        :ir="ir"
        :pir="pir"
        :originators="originators"
    />
</template>
<script setup lang="ts">
import dayjs from 'dayjs';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminIRStore } from '@/stores/admin/ir.store';
import ViewIRDetailsPopup from './ViewIRDetailsPopup.vue';
import { ref } from 'vue';
import type { IPir } from '@/types/Pir';
import type { IOriginator } from '@/types/Originator.type';

const { showSnackbar } = useSnackbar();
const adminIrStore = useAdminIRStore();
const isViewingDetails = ref(false);
const emits = defineEmits(['refreshPirs', 'showIrEditPopup']);

const props = defineProps({
    pir: {
        type: Object as () => IPir,
        required: true,
    },
    ir: {
        type: Object,
        required: true,
    },
    originators: {
        type: Array as () => IOriginator[],
        required: true,
    },
});

const deleteIR = async (id: number | string) => {
    const confirmed = confirm('Are you sure you want to delete this IR?');
    if (!confirmed) return;

    await adminIrStore.deleteIr(id.toString());

    showSnackbar({
        text: 'Information Requirement Deleted',
        color: 'success',
    });
    emits('refreshPirs');
};

const showIrEditPopup = () => {
    emits('showIrEditPopup', props.ir.id);
};
</script>

<style scoped>
.compact-table th,
.compact-table td {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
    font-size: 0.95rem;
}
.compact-table th {
    font-weight: 600;
}
.compact-table td {
    vertical-align: middle;
}
.nowrap-col {
    white-space: nowrap !important;
}
</style>
