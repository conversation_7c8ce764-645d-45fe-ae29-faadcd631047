<template>
    <thead>
        <tr class="font-weight-bold bg-grey-lighten-4 compact-table">
            <th>
                <template
                    v-if="
                        pir &&
                        pir.informationRequirements &&
                        pir.informationRequirements?.length > 0
                    "
                >
                    <v-btn
                        @click="sectionExpanded = !sectionExpanded"
                        variant="plain"
                        color="black"
                        class="mr-2"
                        size="medium"
                    >
                        <v-icon
                            v-if="sectionExpanded"
                            icon="mdi-chevron-down"
                            color="black"
                        />
                        <v-icon
                            v-else
                            icon="mdi-chevron-right"
                            color="black"
                        />
                    </v-btn>
                </template>
            </th>
            <th class="text-center">{{ pir.pirNumber }}</th>
            <th class="text-center">
                <!-- <LumioPriorityChip :priority="pir.priority" /> -->
            </th>
            <!-- <th class="text-center">
                <LumioStatusChip
                    :status="pir.isActive ? 'active' : 'inactive'"
                />
            </th> -->
            
            <th class="text-left">
                <span class="cursor-pointer" @click="isViewingDetails = true">{{ pir.question }}</span>
            </th>
            <th class="text-center">
                {{pir.originator}}
            </th>
            <th class="text-left"></th>
         
            <th v-if="!props.noIrs" class="text-center nowrap-col">
                <v-btn
                    v-if="pir.isActive"
                    @click="addInformationRequirement"
                    variant="flat"
                    size="small"
                    class="ml-2"
                    color="primary"
                >
                    <v-icon>mdi-plus</v-icon>
                </v-btn>
                <v-btn
                    variant="flat"
                    color="primary"
                    size="small"
                    class="ml-2"
                    @click="showEditPopup"
                >
                    <v-icon>mdi-pencil</v-icon>
                </v-btn>
            </th>
        </tr>
    </thead>
    <tbody v-show="sectionExpanded">
        <IrRow
            v-for="ir in pir.informationRequirements"
            :key="ir.id"
            :ir="ir"
            :pir="pir"
            :originators="originators"
            @refreshPirs="refreshPirs"
            @showIrEditPopup="showIrEditPopup"
        ></IrRow>
    </tbody>
    <ViewPIRDetailsPopup
        v-if="isViewingDetails"
        v-model="isViewingDetails"
        :pir="pir"
        :originators="originators"
    />
    
    <v-dialog v-model="isAddingIr" max-width="800">
        <v-card
            prepend-icon="mdi-tooltip-edit-outline"
            title="Add Information Requirement"
        >
            <v-card-text v-if="pir && pir.operationId">
                <add-ir-modal
                    v-if="isAddingIr"
                    :pir-id="pir?.id?.toString() ?? ''"
                    :operation-id="pir?.operationId?.toString()"
                    @close-ir-modal="isAddingIr = false"
                    @create-ir="createIR"
                />
            </v-card-text>
        </v-card>
    </v-dialog>
    <v-dialog v-model="changingIr" max-width="800">
        <v-card
            prepend-icon="mdi-tooltip-edit-outline"
            title="Change Information Requirement"
        >
            <v-card-text v-if="pir && pir.operationId">
                <edit-ir-modal
                    v-if="changingIr"
                    :pir-id="pir?.id?.toString() ?? ''"
                    :operation-id="pir?.operationId?.toString()"
                    @close-ir-modal="changingIr = false"
                    @update-ir="updateIR"
                    :parent-pir="pir"
                    :ir-id="irId"
                />
            </v-card-text>
        </v-card>
    </v-dialog>
    <v-dialog v-model="isEditing" max-width="800">
        
                <edit-p-i-r-popup
                    v-if="isEditing && pir && pir.id && pir.operationId"
                    :pir-id="pir.id?.toString()"
                    :operation-id="pir.operationId?.toString()"
                    @close-update-popup="isEditing = false"
                    @update-pirs="refreshPirs"
                />
            
    </v-dialog>
</template>
<script setup lang="ts">
import { IPir } from '@/types/Pir';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminPirStore } from '@/stores/admin/pir.store';
import { useAdminIRStore } from '@/stores/admin/ir.store';
import IrRow from '@/components/operations/pirs/IrRow.vue';
import { ref } from 'vue';
import ViewPIRDetailsPopup from './ViewPIRDetailsPopup.vue';
const { showSnackbar } = useSnackbar();
const adminPirStore = useAdminPirStore();
const adminIrStore = useAdminIRStore();
const isAddingIr = ref(false);
const isEditing = ref(false);
const sectionExpanded = ref(true);
const changingIr = ref(false);
const irId = ref<string | number | null>(null);
const isViewingDetails = ref(false);

const props = defineProps<{
    pir: IPir;
    noIrs?: boolean;
    originators: any[];
}>();

const emit = defineEmits(['fetch-pirs']);

const addInformationRequirement = () => {
    isAddingIr.value = true;
};

const showEditPopup = () => {
    isEditing.value = true;
};

const showIrEditPopup = (irIdEditable: string | number) => {
    changingIr.value = true;
    irId.value = irIdEditable;
};

const createIR = async (ir: any) => {
    //make sure ir required fields are set
    if (!ir.ltiovDate || !ir.priority || !ir.informationRequirement) {
        showSnackbar({
            text: 'Please fill all required fields',
            color: 'error',
        });
        return;
    }
    ir.pirId = props.pir.id;
    ir.operationId = props.pir.operationId;
    isAddingIr.value = true;

    await adminPirStore.addIrToPir(ir);
    isAddingIr.value = false;
    refreshPirs();
};

const updateIR = async (ir: any) => {
    await adminIrStore.updateIr(ir.irId as string, ir);
    refreshPirs();
    changingIr.value = false;
};

const refreshPirs = () => {
    emit('fetch-pirs');
};
</script>

<style scoped>
.compact-table th,
.compact-table td {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
    font-size: 0.95rem;
}
.compact-table th {
    font-weight: 600;
}
.compact-table td {
    vertical-align: middle;
}
.nowrap-col {
    white-space: nowrap !important;
}
.pir-question-link {
    cursor: pointer;
    text-decoration: underline;
    color: #1976d2;
}
.pir-question-link:hover {
    text-decoration: underline;
    color: #125199;
}
</style>
