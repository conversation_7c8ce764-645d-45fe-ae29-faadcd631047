<template>
    <v-dialog
        v-model="isOpen"
        max-width="900"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card class="ma-3 bg-white border border-solid border-grey-lighten-4 rounded-lg overflow-visible">
            <v-card-title class="d-flex justify-space-between">
                <h3>RFI Details</h3>
                <v-btn
                    class="ma-2"
                    variant="flat"
                    icon
                    density="compact"
                    @click="closePopup"
                >
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>
            <v-card-text>
                <v-container fluid class="pa-0 text-left">
                    <v-row class="mb-2">
                        <v-col cols="12" md="8">
                            <div class="mb-2">
                                <strong>Title:</strong>
                                <div>{{ rfiDetails.title }}</div>
                            </div>
                        </v-col>
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>Priority:</strong>
                                <LumioPriorityChip :priority="String(rfiDetails.priority ?? '')" :fullWidth="false" />
                            </div>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>Status:</strong>
                                <v-chip
                                    color="primary"
                                    text-color="white"
                                    small
                                    class="ml-2"
                                >
                                    {{ rfiDetails.status }}
                                </v-chip>
                            </div>
                        </v-col>
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>Originator:</strong>
                                <div>{{ getOriginatorLabel(rfiDetails.originatorId ? String(rfiDetails.originatorId) : '') }}</div>
                            </div>
                        </v-col>
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>LTIOV Date:</strong>
                                <div>{{ formatDate(rfiDetails.ltiovDate) }}</div>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <div class="mb-2">
                                <strong>Check Source:</strong>
                                <v-icon :color="rfiDetails.checkSource ? 'green' : 'grey'">
                                    {{ rfiDetails.checkSource ? 'mdi-check-circle' : 'mdi-close-circle' }}
                                </v-icon>
                            </div>
                        </v-col>
                    </v-row>
                    <v-divider class="my-4"></v-divider>
                    <v-row>
                        <v-col cols="12">
                            <div class="mb-2">
                                <strong>Justification:</strong>
                                <div style="white-space: pre-line;">{{ rfiDetails.justification }}</div>
                            </div>
                        </v-col>
                    </v-row>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, watch } from 'vue';
import { IRFI } from '@/types/RFI.type';
import { IOriginator } from '@/types/Originator.type';
import { Priority, RFIStatus } from '@/types/Global.type';
import dayjs from 'dayjs';
import LumioPriorityChip from '@/components/elements/LumioPriorityChip.vue';

const emit = defineEmits(['close-popup']);
const props = defineProps<{
    rfi: IRFI | null;
    originators: IOriginator[];
    modelValue: boolean;
}>();

const isOpen = ref(props.modelValue);
watch(() => props.modelValue, (val) => { isOpen.value = val; });
watch(isOpen, (val) => { if (!val) emit('close-popup'); });

const rfiDetails = ref<IRFI>({
    id: '',
    title: '',
    ltiovDate: '',
    originatorId: '',
    originatorLabel: '',
    priority: Priority.NONE,
    operationId: '',
    checkSource: false,
    status: RFIStatus.CREATED,
    justification: '',
});

function closePopup() {
    isOpen.value = false;
}

function getOriginatorLabel(originatorId: string) {
    const found = props.originators.find(o => o.id === originatorId);
    return found ? found.title : originatorId;
}

function formatDate(date: string) {
    return date ? dayjs(date).format('MMM D, YYYY HH:mm') : 'N/A';
}

onMounted(() => {
    if (props.rfi) {
        rfiDetails.value = { ...props.rfi };
    }
});
</script> 