<template>
	<div class="h-100">
	<div
		id="esriMapSingleAreaEditor"
		:style="maxHeight ? `height:${getHeight(maxHeight)}; max-height: ${getHeight(maxHeight)};` : ''"
	>
		<div id="measurements" class="esri-widget h-100"></div>
	</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch, nextTick } from 'vue';
import esriConfig from '@arcgis/core/config';
import Map from '@arcgis/core/Map';
import MapView from '@arcgis/core/views/MapView';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import Sketch from '@arcgis/core/widgets/Sketch';
import BasemapToggle from '@arcgis/core/widgets/BasemapToggle';
import Fullscreen from '@arcgis/core/widgets/Fullscreen';
import ScaleBar from '@arcgis/core/widgets/ScaleBar';
import Graphic from '@arcgis/core/Graphic';
import { SymbolItem } from '@/types/EsriMap';
import { Polygon } from '@arcgis/core/geometry';
import { CIMSymbol } from '@arcgis/core/symbols';
import PopupTemplate from '@arcgis/core/PopupTemplate';
import * as reactiveUtils from '@arcgis/core/core/reactiveUtils';
import { getHeight } from '@/composables/esri/MapGraphicLayers';

interface ExtendedGraphic extends __esri.Graphic {
	uid?: string;
	geometry: __esri.Geometry & {
		paths?: number[][];
		rings?: number[][];
		type: string;
	};
	attributes: {
		isUserMade?: boolean;
		name?: string;
		description?: string;
		id?: number | null;
		[key: string]: any;
	};
}

interface ExtendedHit {
	graphic: ExtendedGraphic;
	type: string;
}

const props = withDefaults(
	defineProps<{
		maxHeight?: number | null | string;
		mapItem?: SymbolItem | null;
		centerCoordinates?: number[];
		zoom?: number | string;
	}>(),
	{
		maxHeight: null,
		mapItem: null,
		centerCoordinates: undefined,
		zoom: 10
	}
);

const locationCountryData = ref<{ country: string; countryCode: string; } | null>(null);


const emit = defineEmits([
	'area-created',
	'area-selected',
	'area-updated',
	'area-deleted',
	'zoom-updated',
	'center-updated',
	'area-lookedup'
]);

let view: MapView;
let map: Map;
let graphicsLayer: GraphicsLayer;
let sketch: Sketch;

const handleAreaDeleted = () => {
	// Show the sketch tool again
	if (sketch) {
		sketch.visible = true;
	}
	emit('area-deleted');
	// Clear the mapItem prop by emitting null
	emit('area-updated', { coordinates: [] });
	// Force a view refresh
	view.goTo(view.viewpoint);
};

const initializeMap = async () => {
	if (view) {
		view.destroy();
	}
	if (graphicsLayer && map) {
		map.remove(graphicsLayer);
	}

	await nextTick();

	map = new Map({
		basemap: 'gray'
	});

	graphicsLayer = new GraphicsLayer();
	map.add(graphicsLayer);

	// Initialize map view
	view = new MapView({
		container: 'esriMapSingleAreaEditor',
		map: map,
		zoom: Number(props.zoom),
		center: props.centerCoordinates ?? [-118.805, 34.027],
		popupEnabled: true,
		constraints: {
			rotationEnabled: false
		}
	});

	// Add keyboard delete handler
	view.on('key-down', (event) => {
		if (event.key === 'Delete' || event.key === 'Backspace') {
			event.stopPropagation();
			// If there's a selected graphic, delete it
			if (sketch.viewModel.state === 'active' && sketch.viewModel.updateGraphics.length > 0) {
				const graphicToDelete = sketch.viewModel.updateGraphics.getItemAt(0);
				if (graphicToDelete) {
					// Remove the graphic from the layer
					graphicsLayer.remove(graphicToDelete);
					
					// Clear any selected graphics
					sketch.viewModel.updateGraphics.removeAll();
					
					// Clear the graphics layer
					graphicsLayer.removeAll();

					handleAreaDeleted();
				}
			}
		}
	});

	// Initialize and configure the Sketch widget for rectangle only
	sketch = new Sketch({
		layer: graphicsLayer,
		view: view,
		availableCreateTools: ['rectangle'],
		creationMode: 'single',
		visibleElements: {
			createTools: {
				rectangle: true,
				polygon: false,
				polyline: false,
				point: false,
				circle: false
			},
			selectionTools: {
				'rectangle-selection': false,
				'lasso-selection': false
			},
			settingsMenu: false,
			undoRedoMenu: false,
			duplicateButton: false
		},
		defaultUpdateOptions: {
			toggleToolOnClick: true,
			multipleSelectionEnabled: false
		}
	});

	// Add widgets to the view
	view.ui.add(sketch, 'top-left');

	const basemapToggle = new BasemapToggle({
		view: view,
		nextBasemap: 'satellite'
	});
	view.ui.add(basemapToggle, 'top-right');

	const fullscreen = new Fullscreen({ view });
	view.ui.add(fullscreen, 'top-right');

	const scalebar = new ScaleBar({ view, unit: 'metric' });
	view.ui.add(scalebar, 'bottom-right');

	// Watch for center and zoom changes
	view.watch('center', (center) => {
		emit('center-updated', {
			latitude: center.latitude,
			longitude: center.longitude,
		});


	});

	view.watch('zoom', (zoom) => {
		emit('zoom-updated', zoom);
	});

	view.on("drag", ["Shift"], (event) => {
		// When dragging ends (mouse up after drag)
		if (event.action === "end" && event.stopPropagation) {
			event.stopPropagation();

			// Find the selected graphic
			if (sketch.updateGraphics.length > 0) {
				const graphic = sketch.updateGraphics.getItemAt(0);
				const polygon = graphic.geometry as Polygon;
				const coordinates = polygon.rings[0];

				emit('area-updated', {
					coordinates: coordinates,
					type: 'polygon'
				});
			}
		}
	});

	// Handle sketch events
	sketch.on('create', (event) => {
		if (event.state === 'complete') {
			const graphic = event.graphic;

			// Create attributes for the graphic
			graphic.attributes = {
				id: null,
				title: 'Area',
				name: 'Area',
				itemType: 'area',
				type: 'polygon',
				coordinates: [],
				isUserMade: true
			};

			// Apply symbol
			graphic.symbol = genSymbol({
				id: null,
				type: 'polygon',
				coordinates: [],
				title: 'Area',
				elementsColor: [111, 111, 111, 89],
				elementsBorderColor: [111, 111, 111, 255],
				elementsBorderThickness: 2,
				elementsBorderType: 'solid'
			});

			// Hide sketch tool since we only want one rectangle
			sketch.visible = false;

			// Extract coordinates for the event emission
			const polygon = graphic.geometry as Polygon;
			const coordinates = polygon.rings[0];

			// Get location data for the center of the polygon
			const centerX = coordinates.reduce((sum, point) => sum + point[0], 0) / coordinates.length;
			const centerY = coordinates.reduce((sum, point) => sum + point[1], 0) / coordinates.length;
			getLocationCountryData([centerX, centerY]);

			// Emit the created area event
			emit('area-created', {
				coordinates: coordinates,
				type: 'polygon'
			});
		}
	});

	// Handle update events (resize, move)
	sketch.on('update', (event) => {
		// This will detect the end of a move/resize operation
		if (event.state === 'start') {
			// Optional: track that editing has started
		} else if (event.state === 'active' && event.toolEventInfo && (event.toolEventInfo.type === 'move-stop' || event.toolEventInfo.type === 'scale-stop')) {
			// Track active editing but don't emit yet
			const graphic = event.graphics[0];
			const polygon = graphic.geometry as Polygon;
			const coordinates = polygon.rings[0];

			// Get location data for the center of the polygon
			const centerX = coordinates.reduce((sum, point) => sum + point[0], 0) / coordinates.length;
			const centerY = coordinates.reduce((sum, point) => sum + point[1], 0) / coordinates.length;
			getLocationCountryData([centerX, centerY]);

			emit('area-updated', {
				coordinates: coordinates,
				type: 'polygon'
			});
		} else if (event.state === 'complete' && event.graphics.length > 0) {
			const graphic = event.graphics[0];
			const polygon = graphic.geometry as Polygon;
			const coordinates = polygon.rings[0];

			// Get location data for the center of the polygon
			const centerX = coordinates.reduce((sum, point) => sum + point[0], 0) / coordinates.length;
			const centerY = coordinates.reduce((sum, point) => sum + point[1], 0) / coordinates.length;
			getLocationCountryData([centerX, centerY]);

			emit('area-updated', {
				coordinates: coordinates,
				type: 'polygon'
			});
		}
	});

	// Handle popup actions
	view.when(() => {
		reactiveUtils.on(
			() => view.popup,
			'trigger-action',
			(event) => {
				console.log('Action triggered:', event.action.id);
				if (event.action.id === 'delete-area') {
					const graphic = view.popup.features[0];
					console.log('Deleting graphic:', graphic);

					if (graphic) {
						// Remove the graphic from the layer
						graphicsLayer.remove(graphic);
						
						// Clear any selected graphics
						sketch.viewModel.updateGraphics.removeAll();
						
						// Clear the graphics layer
						graphicsLayer.removeAll();

						// Close the popup
						view.popup.close();

						handleAreaDeleted();
					}
				}
			}
		);
	});

	// Set up click handler
	view.on('click', (event) => {
		view.hitTest(event).then((response) => {
			try {
				const arrayOfGraphics = response.results as ExtendedHit[];
				const element = arrayOfGraphics.find(item => {
					return (item.graphic?.attributes?.isUserMade);
				});

				if (!element) return;
				const graphic: ExtendedGraphic = element.graphic;
				if (graphic) {
					graphic.popupTemplate = new PopupTemplate({
						title: "Area",
						content: [{
							type: 'text',
							text: 'Click delete to remove this area.'
						}],
						actions: [
							{
								type: 'button',
								title: "DELETE AREA",
								id: 'delete-area',
								icon: 'trash',
								className: 'esri-popup__button--danger'
							}
						]
					});

					view.openPopup({
						location: event.mapPoint,
						features: [graphic]
					});
				}
			} catch (error) {
				console.error('Error handling click:', error);
			}
		});
	});

	// If mapItem is provided, add it to the map
	if (props.mapItem) {
		//make sure this item has coordinates and coordinates exist lenght over 0
		if(!props.mapItem.coordinates || props.mapItem.coordinates.length === 0) {
			// Show sketch tool if no coordinates
			if (sketch) {
				sketch.visible = true;
			}
			return;
		}

		addItem(props.mapItem);
		// Only hide sketch if we have coordinates
		if (sketch && props.mapItem.coordinates.length > 0) {
			sketch.visible = false;
		}
	}
};

// Generate symbol for the area
const genSymbol = (item: SymbolItem) => {
	return new CIMSymbol({
		data: {
			type: 'CIMSymbolReference',
			symbol: {
				type: 'CIMPolygonSymbol',
				symbolLayers: [
					{
						type: 'CIMSolidFill',
						enable: true,
						color: [...(item.elementsColor ?? [0, 0, 0, 40])]
					},
					{
						type: 'CIMSolidStroke',
						enable: true,
						color: [...(item.elementsBorderColor ?? [0, 0, 0, 255])],
						width: item.elementsBorderThickness ?? 2,
						capStyle: 'Round',
						joinStyle: 'Round',
						miterLimit: 10,
						effects: item.elementsBorderType === 'dashed' ? [
							{
								type: 'CIMGeometricEffectDashes',
								dashTemplate: [4, 4],
								lineDashEnding: 'NoConstraint',
								//@ts-ignore
								controlPointEnding: 'NoConstraint'
							}
						] : []
					}
				]
			}
		}
	});
};

// Add single item to map
const addItem = async (item: SymbolItem | null) => {
	console.log('Adding item to map:', item);
	
	// If no item or no valid coordinates, just show the sketch tool
	if (!item || !item.coordinates || !item.coordinates.length || !graphicsLayer) {
		console.log('No valid item to add, showing sketch tool');
		if (sketch) {
			sketch.visible = true;
		}
		return;
	}

	// Clear existing graphics
	graphicsLayer.removeAll();

	// Ensure coordinates are in the correct format
	const coordinates = Array.isArray(item.coordinates[0]) 
		? (item.coordinates[0] as number[][]) 
		: (item.coordinates as number[][]);

	console.log('Using coordinates:', coordinates);

	const polygonGraphic = new Graphic({
		geometry: new Polygon({
			rings: [coordinates],
			spatialReference: { wkid: 102100 }
		}),
		symbol: genSymbol(item),
		attributes: {
			name: item.title ?? 'Area',
			title: item.title ?? 'Area',
			description: item.description ?? '',
			itemType: 'area',
			id: item.id ?? null,
			coordinates: coordinates,
			isUserMade: true,
			type: 'polygon'
		}
	});

	graphicsLayer.add(polygonGraphic);

	// Get coordinates of the first point of the polygon
	const firstPoint = coordinates[0];
	console.log('First point coordinates:', firstPoint);
	
	if(firstPoint){
		await getLocationCountryData(firstPoint as number[]);
	}
};

// Watch for changes in mapItem prop
watch(() => props.mapItem, (newItem) => {
	if (!graphicsLayer) return;

	// If no item or no valid coordinates, show the sketch tool
	if (!newItem || !newItem.coordinates || !newItem.coordinates.length) {
		if (sketch) {
			sketch.visible = true;
		}
		return;
	}

	// Only add the item if it's not already in the layer
	const existingGraphic = graphicsLayer.graphics.find(g => 
		g.attributes?.id === newItem?.id || 
		(g.attributes?.coordinates && 
		 JSON.stringify(g.attributes.coordinates) === JSON.stringify(newItem?.coordinates))
	);

	if (!existingGraphic) {
		addItem(newItem);
		// Only hide sketch if we have valid coordinates
		if (sketch && newItem.coordinates && newItem.coordinates.length > 0) {
			sketch.visible = false;
		}
	}
}, { deep: true });

//create function to get location country data from the center coordinates
const getLocationCountryData = async (centerCoordinates: number[]) => {
	try {
		//do not get it if it's 0,0
		console.log('item coordinates', centerCoordinates);
		if(centerCoordinates[0] === 0 && centerCoordinates[1] === 0) return;

		const [x, y] = centerCoordinates;
		
		// Convert from Web Mercator (EPSG:3857) to WGS84 (EPSG:4326)
		const longitude = (x * 180) / 20037508.34;
		const latitude = (Math.atan(Math.exp((y * Math.PI) / 20037508.34)) * 2 - Math.PI / 2) * (180 / Math.PI);
		
		// Ensure coordinates are within valid ranges
		if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90) {
			console.error('Invalid coordinates after conversion:', { longitude, latitude });
			return;
		}

		console.log('Fetching location data for coordinates:', { longitude, latitude });

		// Use Nominatim reverse geocoding service with proper coordinate formatting
		const response = await fetch(
			`https://nominatim.openstreetmap.org/reverse.php?lat=${latitude.toFixed(6)}&lon=${longitude.toFixed(6)}&zoom=18&format=jsonv2`,
			{
				headers: {
					'Accept-Language': 'en',
					'User-Agent': 'LumioApp/1.0'
				}
			}
		);

		if (!response.ok) {
			console.error('Nominatim API response not OK:', response.status, response.statusText);
			throw new Error(`Failed to fetch location data: ${response.status} ${response.statusText}`);
		}

		const data = await response.json();
		console.log('Nominatim response:', data);

		// Check if we got valid address data
		if (!data.address || !data.address.country) {
			console.error('No country data found in response:', data);
			return;
		}

		const countryData = {
			country: data.address.country,
			countryCode: data.address.country_code.toUpperCase() // Convert to uppercase for consistency
		};

		console.log('Parsed country data:', countryData);
		locationCountryData.value = countryData;
		emit('area-lookedup', countryData);
	} catch (error) {
		console.error('Error fetching location data:', error);
		locationCountryData.value = null;
	}
};

// Setup and cleanup
onMounted(async () => {
	esriConfig.apiKey = import.meta.env.VITE_ARCGIS_API_KEY;
	await initializeMap();
	// if(props.centerCoordinates){
	// 	await getLocationCountryData(props.centerCoordinates);
	// }
});

onUnmounted(() => {
	if (view) {
		view.destroy();
	}
});

</script>

<style scoped lang="scss">
@import 'https://js.arcgis.com/4.30/@arcgis/core/assets/esri/themes/light/main.css';

#esriMapEditor{
	min-height: 200px;
}

#esriMapSingleAreaEditor,
html,
body,
.arcgis-map {
	height: 100%;
	width: 100%;
	margin: 0;
	padding: 0;
}
</style>
