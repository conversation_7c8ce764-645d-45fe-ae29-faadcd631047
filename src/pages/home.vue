<template>
    <div class="px-4" style="margin-top: 20px">
        <v-row>
            <!-- Left side - Tasks list -->
            <v-col cols="7">
                <v-card class="lumio-card h-100">
                    <v-card-title
                        class="d-flex justify-space-between align-center"
                    >
                        <h4>List of tasks</h4>
                        <v-btn
                            color="primary"
                            size="small"
                            @click="openCreateTaskModal"
                            ref="createTaskButton"
                        >
                            <v-icon left>mdi-plus</v-icon>
                            Create Task
                        </v-btn>
                    </v-card-title>
                    <v-card-text class="task-list-container">
                        <v-data-table
                            :headers="taskHeaders"
                            :items="tasks"
                            :loading="isTasksLoading"
                            :items-per-page="10"
                            :page="currentPage"
                            :items-length="totalTasks"
                            @update:page="handlePageChange"
                            density="compact"
                            hover
                            class="task-table"
                            fixed-header
                            height="calc(300px - 48px)"
                            ref="taskTable"
                        >
                            <template v-slot:item.priority="{ item }">
                                <LumioPriorityChip
                                    :priority="item.priority as 'high' | 'medium' | 'low' | 'none' | 'highest'"
                                />
                            </template>

                            <template v-slot:item.members="{ item }">
                                <div class="d-flex align-center">
                                    <div
                                        v-if="item.members?.length"
                                        class="d-flex align-center"
                                    >
                                        <v-tooltip
                                            v-for="member in item.members as ITaskMember[]"
                                            :key="member.id"
                                            :text="`${member.email} ID: #${member.id}`"
                                            location="top"
                                        >
                                            <template
                                                v-slot:activator="{ props }"
                                            >
                                                <v-avatar
                                                    v-bind="props"
                                                    size="28"
                                                    color="primary"
                                                    class="mr-1"
                                                >
                                                    {{ getInitials(member) }}
                                                </v-avatar>
                                            </template>
                                        </v-tooltip>
                                    </div>
                                    <div v-else class="d-flex align-center">
                                        <span class="text-caption mr-2"
                                            >No members</span
                                        >
                                        <v-btn
                                            size="x-small"
                                            color="blue"
                                            variant="text"
                                            class="ml-1 text-caption"
                                            @click.stop="addMembers(item)"
                                        >
                                            Add
                                        </v-btn>
                                    </div>
                                </div>
                            </template>

                            <template v-slot:item.createdByUser="{ item }">
                                <span class="text-caption"
                                    >User #{{ item.createdByUserId }}</span
                                >
                            </template>

                            <template v-slot:item.createdAt="{ item }">
                                {{ formatDate(item.createdAt || '') }}
                            </template>

                            <template v-slot:item.actions="{ item }">
                                <v-btn
                                    icon="mdi-pencil"
                                    size="small"
                                    variant="text"
                                    color="primary"
                                    @click="editTask(item)"
                                ></v-btn>
                                <v-btn
                                    icon="mdi-delete"
                                    size="small"
                                    variant="text"
                                    color="error"
                                    @click="deleteTask(item)"
                                ></v-btn>
                                <v-btn
                                    icon="mdi-arrow-right"
                                    size="small"
                                    variant="text"
                                    color="primary"
                                    @click="viewTaskDetails(item)"
                                ></v-btn>
                            </template>
                        </v-data-table>
                    </v-card-text>
                </v-card>
            </v-col>

            <!-- Right side - PIRs list -->
            <v-col cols="5">
                <v-card class="lumio-card h-100">
                    <v-card-title
                        class="d-flex justify-space-between align-center"
                    >
                        <h4>Collection Requirements (PIRs)</h4>
                        <v-btn
                            v-if="!isLoading"
                            color="primary"
                            size="small"
                            variant="text"
                            @click="fetchPirs"
                        >
                            <v-icon>mdi-refresh</v-icon>
                        </v-btn>
                    </v-card-title>

                    <v-card-text class="pir-list-container">
                        <div
                            v-if="isLoading"
                            class="d-flex justify-center pa-5"
                        >
                            <v-progress-circular
                                :width="3"
                                :size="50"
                                color="primary"
                                indeterminate
                            ></v-progress-circular>
                        </div>

                        <div
                            v-else-if="pirs.length === 0"
                            class="d-flex justify-center pa-5"
                        >
                            <v-alert
                                type="info"
                                color="secondary"
                                density="compact"
                                class="ma-0 pa-5"
                                elevation="0"
                            >
                                No PIRs found
                            </v-alert>
                        </div>

                        <v-list
                            v-else
                            lines="two"
                            density="compact"
                            class="pir-list"
                        >
                            <v-list-item
                                v-for="pir in pirs"
                                :key="pir.id"
                                :title="pir.question"
                                :subtitle="`PIR #${pir.pirNumber} - ${pir.originator}`"
                                class="mb-2"
                            >
                                <template v-slot:prepend>
                                    <LumioPriorityChip
                                        :priority="pir.priority as 'high' | 'medium' | 'low' | 'none' | 'highest'"
                                        class="mr-2"
                                    />
                                </template>

                                <template v-slot:append>
                                    <v-btn
                                        size="small"
                                        variant="text"
                                        color="primary"
                                        @click="viewPirDetails(pir)"
                                    >
                                        <v-icon>mdi-eye</v-icon>
                                    </v-btn>
                                </template>
                            </v-list-item>
                        </v-list>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>

        <!-- Calendar Section -->
        <v-row ref="calendarRow" class="mt-4">
            <v-col cols="12">
                <v-card class="lumio-card h-100">
                    <v-card-text class="calendar-container">
                        <div
                            v-if="isTasksLoading"
                            class="d-flex justify-center pa-5"
                        >
                            <v-progress-circular
                                :width="3"
                                :size="50"
                                color="primary"
                                indeterminate
                            ></v-progress-circular>
                        </div>
                        <TasksCalendar
                            v-else
                            style="height: 400px; overflow-y: scroll"
                            :tasks="tasks"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </div>

    <!-- Task Modal -->
    <CreateTaskModal
        v-model="showTaskModal"
        :taskId="selectedTaskId"
        @task-created="handleTaskCreated"
        @task-updated="handleTaskUpdated"
    />

    <!-- Assign Members Modal -->
    <AssignUserTaskModal
        v-if="selectedTaskForMembers"
        :model-value="showAssignMembersModal"
        :taskId="selectedTaskForMembers"
        @update:model-value="
            (value) => {
                showAssignMembersModal = value;
                if (!value) fetchTasks();
            }
        "
    />
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
</route>

<script lang="ts" setup>
import { useAccessControl } from '@/composables/useAccessControl';
import { onMounted, ref, nextTick } from 'vue';
import { useAdminPirStore } from '@/stores/admin/pir.store';
import { IPir } from '@/types/Pir';
import type { IPagination } from '@/types/Global.type';
import { useRouter } from 'vue-router';
import CreateTaskModal from '@/components/tasks/CreateTaskModal.vue';
import AssignUserTaskModal from '@/components/tasks/AssignUserTaskModal.vue';
import TasksCalendar from '@/components/tasks/TasksCalendar.vue';
import { useSnackbar } from '@/composables/useSnackbar';
import { useTaskStore, ITask } from '@/stores/admin/task.store';
import { useShepherd } from 'vue-shepherd';
import type { ComponentPublicInstance } from 'vue';

const router = useRouter();
useAccessControl();

const tour = useShepherd({
    useModalOverlay: true,
    defaultStepOptions: {
        classes: 'shepherd-theme-custom',
        scrollTo: true,
        cancelIcon: {
            enabled: true,
        },
        buttons: [
            {
                text: 'Back',
                action: () => tour.back(),
                classes: 'shepherd-button-secondary',
                disabled: true,
            },
            {
                text: 'Next',
                action: () => tour.next(),
                classes: 'shepherd-button-primary',
            },
        ],
    },
});

const adminPirStore = useAdminPirStore();
const taskStore = useTaskStore();
const pirs = ref<IPir[]>([]);
const isLoading = ref(true);
const isTasksLoading = ref(false);
const showTaskModal = ref(false);
const showAssignMembersModal = ref(false);
const selectedTaskId = ref<number | undefined>(undefined);
const selectedTaskForMembers = ref<number | undefined>(undefined);
const { showSnackbar } = useSnackbar();
const tasks = ref<ITask[]>([]);
const currentPage = ref(1);
const totalTasks = ref(0);

// tour elements
const createTaskButton = ref<ComponentPublicInstance | null>(null);
const taskTable = ref<ComponentPublicInstance | null>(null);
const calendarRow = ref<ComponentPublicInstance | null>(null);

interface ITaskMember {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
}

const taskHeaders = [
    { title: 'Title', key: 'title', sortable: true },
    { title: 'Priority', key: 'priority', sortable: true },
    { title: 'Members', key: 'members', sortable: false },
    { title: 'Created By', key: 'createdByUser', sortable: true },
    { title: 'Created Date', key: 'createdAt', sortable: true },
    {
        title: 'Actions',
        key: 'actions',
        sortable: false,
        align: 'end' as const,
    },
];

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 10,
    total: 0,
    pages: 0,
    sortBy: 'desc',
    orderBy: 'createdAt',
});

const fetchPirs = async () => {
    isLoading.value = true;
    try {
        const pirResponse = await adminPirStore.fetchPirs(
            paginationState.value,
            {},
            ['informationRequirements'],
        );

        if (pirResponse?.data?.pirs) {
            pirs.value = pirResponse.data.pirs as IPir[];
        }
    } catch (error) {
        console.error('Error fetching PIRs:', error);
    } finally {
        isLoading.value = false;
    }
};

const fetchTasks = async () => {
    isTasksLoading.value = true;
    try {
        const response = await taskStore.fetchTasks({
            page: currentPage.value,
            perPage: 10,
            sortBy: 'desc',
            orderBy: 'createdAt',
        });

        if (response?.data?.tasks) {
            tasks.value = response.data.tasks;
            totalTasks.value = response.data.pagination.total;
        }
    } catch (error) {
        console.error('Error fetching tasks:', error);
        showSnackbar({
            text: 'Failed to fetch tasks',
            color: 'error',
        });
    } finally {
        isTasksLoading.value = false;
    }
};

const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchTasks();
};

// Removed unused function

const getInitials = (user: ITaskMember) => {
    return `${user.firstName.charAt(0)}${user.lastName.charAt(
        0,
    )}`.toUpperCase();
};

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
};

const viewPirDetails = (pir: IPir) => {
    router.push(`/admin/operation/rcm#requirements`);
};

const openCreateTaskModal = () => {
    selectedTaskId.value = undefined;
    showTaskModal.value = true;
};

const editTask = (task: ITask) => {
    selectedTaskId.value = task.id;
    showTaskModal.value = true;
};

const deleteTask = async (task: ITask) => {
    const confirmDelete = window.confirm(
        `Are you sure you want to delete the task "${task.title}"?`,
    );

    if (!confirmDelete) return;

    try {
        await taskStore.deleteTask(task.id);
        showSnackbar({
            text: 'Task deleted successfully',
            color: 'success',
        });
        fetchTasks();
    } catch (error) {
        console.error('Error deleting task:', error);
        showSnackbar({
            text: 'Failed to delete task',
            color: 'error',
        });
    }
};

const viewTaskDetails = (task: ITask) => {
    router.push(`/task/${task.id}`);
};

const handleTaskCreated = () => {
    showSnackbar({
        text: 'Task created successfully',
        color: 'success',
    });
    fetchTasks();
};

const handleTaskUpdated = () => {
    showSnackbar({
        text: 'Task updated successfully',
        color: 'success',
    });
    fetchTasks();
};

const addMembers = (task: ITask) => {
    if (task.id) {
        selectedTaskForMembers.value = task.id;
        showAssignMembersModal.value = true;
    }
};

onMounted(async () => {
    await Promise.all([fetchPirs(), fetchTasks()]);

    // Wait for the next tick to ensure elements are rendered
    await nextTick();

    // Only add tour steps if elements exist
    if (createTaskButton.value) {
        tour.addStep({
            attachTo: { element: createTaskButton.value.$el, on: 'top' },
            text: 'Create a new task',
        });
    }

    if (taskTable.value) {
        tour.addStep({
            attachTo: { element: taskTable.value.$el, on: 'top' },
            text: 'Table of tasks',
        });
    }

    if (calendarRow.value) {
        tour.addStep({
            attachTo: { element: calendarRow.value.$el, on: 'top' },
            text: 'Calendar of tasks',
        });
    }

    // Only start the tour if we have steps
    /* if (tour.steps.length > 0) {
        tour.start();
    } */
});
</script>

<style scoped>
.section-title {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.task-table :deep(.v-data-table__tr) {
    height: 40px;
    margin-bottom: 2px;
}

.task-table :deep(.v-data-table__tr:hover) {
    background-color: rgba(var(--v-theme-primary), 0.05);
}

.task-list-container {
    height: 300px;
    overflow: hidden;
}

.task-table :deep(.v-data-table__wrapper) {
    height: calc(300px - 48px) !important;
    overflow-y: auto;
}

.task-table :deep(.v-data-table) {
    height: 100%;
}

.task-table :deep(.v-data-table__th) {
    position: sticky;
    top: 0;
    background-color: rgb(var(--v-theme-surface));
    z-index: 1;
}

.pir-list-container {
    height: 300px;
    overflow: hidden;
}

.pir-list {
    height: 100%;
    overflow-y: auto;
}

.calendar-container {
    height: 600px;
    overflow: hidden;
}

:deep(.calendar-container) {
    height: 100%;
}

:deep(.calendar-header) {
    padding: 8px;
}

:deep(.calendar-controls) {
    margin-bottom: 8px;
}

:deep(.calendar-body) {
    height: calc(100% - 60px);
    padding: 8px;
    overflow-y: auto;
}

:deep(.calendar-grid) {
    min-height: 200px;
}

:deep(.day-cell) {
    min-height: 30px;
    padding: 4px;
}

:deep(.task-item) {
    margin-bottom: 2px;
}

:deep(.v-card-title) {
    padding: 8px;
}

:deep(.v-card-text) {
    padding: 8px;
}
</style>
