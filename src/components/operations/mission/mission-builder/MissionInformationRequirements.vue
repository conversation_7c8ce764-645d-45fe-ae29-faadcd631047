<template>
    <v-card class="lumio-card">
        <v-alert v-if="unsavedChanges" type="warning">
            There are unsaved changes
        </v-alert>
        <v-card class="lumio-card border">
            <v-card-title>
                <h4>Add Information Requirements</h4>
                <div class="d-flex justify-end">
                    <v-btn
                        color="red"
                        variant="outlined"
                        prepend-icon="mdi-trash-can"
                        append-icon="mdi-arrow-right"
                        @click="discardChanges"
                        v-if="mission.id"
                    >
                        Discard Changes & Next Step
                    </v-btn>

                    <v-btn
                        class="ml-2"
                        color="primary"
                        prepend-icon="mdi-content-save-check"
                        append-icon="mdi-arrow-right"
                        @click="updateMission"
                        v-if="mission.id"
                    >
                        Update Mission & Next Step
                    </v-btn>
                </div>
            </v-card-title>
            <v-card-item>
                <v-row>
                    <v-col cols="4" class="bg-grey-lighten-4">
                        <v-card class="lumio-card bg-white ma-2">
                            <v-card-subtitle class="pa-2">
                                Step 1:
                                <span class="text-grey-darken-4">Pick PIR</span>
                            </v-card-subtitle>
                            <v-card-text class="pa-2">
                                <template
                                    v-if="
                                        mappedPirs.some(
                                            (pir) =>
                                                pir.informationRequirements &&
                                                pir.informationRequirements
                                                    .length > 0,
                                        )
                                    "
                                >
                                    <v-select
                                        label="Select PIR"
                                        :items="mappedPirs"
                                        v-model="currentActivePir"
                                        item-title="question"
                                        item-value="id"
                                        return-object
                                        density="compact"
                                        variant="outlined"
                                    >
                                        <template
                                            v-slot:item="{
                                                item,
                                                props: slotProps,
                                            }"
                                        >
                                            <v-list-item
                                                v-bind="slotProps"
                                                :title="item.raw.question"
                                                class="mb-2"
                                            >
                                                <template v-slot:prepend>
                                                    <span
                                                        class="font-weight-medium"
                                                        >[PIR:
                                                        {{
                                                            item.raw.pirNumber
                                                        }}]&nbsp;</span
                                                    >
                                                </template>
                                                <template v-slot:append>
                                                    <div
                                                        class="d-flex align-center"
                                                    >
                                                        <LumioPriorityChip
                                                            :priority="
                                                                item.raw
                                                                    .priority
                                                            "
                                                            :full-width="false"
                                                        />
                                                        <v-chip
                                                            size="small"
                                                            color="primary"
                                                            v-if="
                                                                item.raw
                                                                    .informationRequirements &&
                                                                item.raw
                                                                    .informationRequirements
                                                                    .length > 0
                                                            "
                                                        >
                                                            {{
                                                                item.raw
                                                                    .informationRequirements
                                                                    .length
                                                            }}
                                                            IRs (Selected:
                                                            {{
                                                                (
                                                                    item.raw ||
                                                                    item
                                                                ).informationRequirements?.filter(
                                                                    (ir: any) =>
                                                                        ir.isSelected,
                                                                ).length
                                                            }})
                                                        </v-chip>
                                                    </div>
                                                </template>
                                            </v-list-item>
                                        </template>
                                        <template v-slot:selection="{ item }">
                                            <div class="d-flex align-center">
                                                <span class="font-weight-medium"
                                                    >[PIR:
                                                    {{
                                                        (item.raw || item)
                                                            .pirNumber
                                                    }}]
                                                </span>
                                                <span class="ml-2">{{
                                                    (item.raw || item).question
                                                }}</span>
                                            </div>
                                        </template>
                                        <template v-slot:details>
                                            Select PIR to add IRs
                                        </template>
                                    </v-select>
                                </template>
                                <template v-else> You have no PIRs. </template>
                            </v-card-text>
                            <v-card-subtitle class="pa-2">
                                Step 2:
                                <span class="text-grey-darken-4">Add IRs</span>
                            </v-card-subtitle>
                            <v-card-text class="pa-2">
                                <div v-if="currentActivePir" class="">
                                    <v-list
                                        v-if="
                                            currentActivePir &&
                                            currentActivePir.informationRequirements &&
                                            currentActivePir
                                                .informationRequirements
                                                .length > 0
                                        "
                                    >
                                        <v-list-item
                                            v-for="item in currentActivePir.informationRequirements"
                                            border
                                            class="mb-2"
                                            density="compact"
                                            lines="two"
                                        >
                                            <v-list-item-title
                                                ><v-badge
                                                    inline
                                                    :label="item.priority"
                                                    :content="item.priority"
                                                    class=""
                                                    size="small"
                                                    :color="
                                                        getPriorityColor(
                                                            item.priority,
                                                        )
                                                    "
                                                ></v-badge>
                                                <strong
                                                    >IR:
                                                    {{
                                                        currentActivePir.pirNumber
                                                    }}.{{
                                                        item.irNumber
                                                    }}</strong
                                                >
                                                -
                                                {{
                                                    item.informationRequirement
                                                }}</v-list-item-title
                                            >
                                            <v-list-item-subtitle
                                                class="text-high-emphasis"
                                                >{{
                                                    item.originator
                                                }}</v-list-item-subtitle
                                            >
                                            <v-list-item-action>
                                            </v-list-item-action>
                                            <template v-slot:append>
                                                <v-list-item-action
                                                    class="flex-column align-end"
                                                >
                                                    <v-icon
                                                        v-if="item.isSelected"
                                                        color="green-darken-3"
                                                        >mdi-check</v-icon
                                                    >
                                                    <v-btn
                                                        v-else
                                                        color="primary"
                                                        append-icon="mdi-chevron-right"
                                                        size="small"
                                                        @click="
                                                            item.isSelected = true
                                                        "
                                                        >Add</v-btn
                                                    >
                                                </v-list-item-action>
                                            </template>
                                        </v-list-item>
                                    </v-list>
                                    <div
                                        v-else
                                        class="text-subtitle-1 text-grey"
                                    >
                                        No information requirements for this PIR
                                    </div>
                                </div>
                                <div
                                    v-else
                                    class="text-subtitle-1 text-grey text-center"
                                >
                                    Please select a PIR to view its information
                                    requirements
                                </div>
                            </v-card-text>
                        </v-card>
                    </v-col>
                    <v-col cols="8">
                        <v-card class="lumio-card" variant="flat">
                            <v-card-title>
                                <h4>Current Mission IRs</h4>
                            </v-card-title>
                            <v-card-text>
                                <v-table density="compact">
                                    <template
                                        v-for="pir in filterSelectedIr"
                                        :key="pir.id"
                                    >
                                        <thead>
                                            <tr class="pa-0 ma-0">
                                                <th class="pa-0 ma-0 pl-2">
                                                    [PIR: #{{ pir.pirNumber }}]
                                                    {{ pir.question }}
                                                </th>
                                                <th class="pa-0 ma-0"></th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-grey-lighten-5">
                                            <tr
                                                v-for="ir in pir.informationRequirements"
                                                :key="ir.id"
                                            >
                                                <td>
                                                    <div
                                                        class="d-flex justify-space-between ml-4"
                                                    >
                                                        <div>
                                                            [{{
                                                                pir.pirNumber
                                                            }}.{{
                                                                ir.irNumber
                                                            }}]
                                                            <em>{{
                                                                ir.informationRequirement
                                                            }}</em>
                                                        </div>
                                                        <LumioPriorityChip
                                                            :priority="
                                                                ir.priority
                                                                    ? ir.priority
                                                                    : ''
                                                            "
                                                            :full-width="false"
                                                        />
                                                    </div>
                                                </td>
                                                <td style="width: 35px">
                                                    <v-btn
                                                        variant="text"
                                                        size="small"
                                                        color="red"
                                                        @click="
                                                            ir.isSelected =
                                                                !ir.isSelected
                                                        "
                                                    >
                                                        <v-icon
                                                            >mdi-trash-can</v-icon
                                                        >
                                                    </v-btn>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </template>
                                </v-table>
                            </v-card-text>
                        </v-card>
                    </v-col>
                </v-row>
            </v-card-item>
        </v-card>
    </v-card>
</template>

<script setup lang="ts">
import { getPriorityColor } from '@/composables/misc.helper';
import { useOperationStore } from '@/stores/operation.store';
import {
    useAdminPirStore,
    IPirCollectionResponse,
} from '@/stores/admin/pir.store';
import { useMissionStore } from '@/stores/admin/mission.store';
import { IPir } from '@/types/Pir';
import { IPagination } from '@/types/Global.type';
import { IIr } from '@/types/IR.type';
import { Mission } from '@/types/Mission';

// Local extension of IIr to add isSelected property
interface IIrWithSelection extends IIr {
    isSelected: boolean;
}

// Local extension of IPir to use IIrWithSelection for informationRequirements
interface IPirWithSelectedIRs extends IPir {
    informationRequirements: IIrWithSelection[];
}

const props = defineProps<{
    mission: Mission;
}>();

const emits = defineEmits(['update:informationRequirements', 'fetch-mission']);

const operationStore = useOperationStore();
const adminPirStore = useAdminPirStore();
const missionStore = useMissionStore();

const pirPagination = ref<IPagination>({
    page: 1,
    pages: 0,
    sortBy: 'desc',
    orderBy: 'id',
    perPage: 100,
    total: 0,
});

const mappedPirs = ref<IPirWithSelectedIRs[]>([]);
const currentActivePir = ref<IPirWithSelectedIRs>();
const informationRequirementIdsToAdd = ref<number[]>([]);
const originalInformationRequirementIds = ref<number[]>([]);

const fetchPirsByOperationId = async () => {
    const { data } = (await adminPirStore.fetchPirs(
        pirPagination.value,
        {
            operationId: operationStore.currentOperationId,
        },
        ['informationRequirements'],
    )) as IPirCollectionResponse;

    pirPagination.value = data?.pagination as IPagination;

    // Process PIRs and add isSelected property to each information requirement
    mappedPirs.value = (
        data?.pirs.map((pir) => {
            if (pir.informationRequirements) {
                // Add isSelected property to each information requirement
                pir.informationRequirements = pir.informationRequirements
                    .map((ir) => ({
                        ...ir,
                        isSelected: props.mission.informationRequirements
                            ? props.mission.informationRequirements.some(
                                  (mir: any) => mir.id === ir.id,
                              )
                            : false,
                    }))
                    .sort((a, b) => {
                        // Define priority order
                        const priorityOrder = {
                            highest: 1,
                            high: 2,
                            medium: 3,
                            low: 4,
                            none: 5,
                        };

                        // First sort by priority
                        const priorityComparison =
                            priorityOrder[
                                a.priority as keyof typeof priorityOrder
                            ] -
                            priorityOrder[
                                b.priority as keyof typeof priorityOrder
                            ];

                        // If priorities are the same, sort by irNumber
                        if (priorityComparison === 0) {
                            return Number(a.irNumber) - Number(b.irNumber);
                        }

                        return priorityComparison;
                    });
            }
            return pir as IPirWithSelectedIRs;
        }) || []
    ).sort((a, b) => {
        // Define priority order
        const priorityOrder = {
            highest: 1,
            high: 2,
            medium: 3,
            low: 4,
            none: 5,
        };

        // First sort by priority
        const priorityComparison =
            priorityOrder[a.priority as keyof typeof priorityOrder] -
            priorityOrder[b.priority as keyof typeof priorityOrder];

        // If priorities are the same, sort by pirNumber
        if (priorityComparison === 0) {
            // Assuming pirNumber is a numeric string, convert to number for comparison
            return Number(a.pirNumber) - Number(b.pirNumber);
        }

        return priorityComparison;
    });

    // Initialize selected IR IDs from mission data
    if (props.mission.id && props.mission.informationRequirements) {
        informationRequirementIdsToAdd.value =
            props.mission.informationRequirements.map((mir: any) => mir.id);
        // Store original IDs for change detection
        originalInformationRequirementIds.value = [
            ...informationRequirementIdsToAdd.value,
        ];
    }
};

const filterSelectedIr = computed(() => {
    return mappedPirs.value
        .map((pir) => {
            const selectedIrs = pir.informationRequirements?.filter(
                (ir) => ir.isSelected,
            );
            return {
                ...pir,
                informationRequirements: selectedIrs,
            };
        })
        .filter((pir) => pir.informationRequirements?.length > 0);
});

// Save changes to the mission
const updateMission = async () => {
    if (!props.mission.id) return;

    try {
        // Add the information requirements to the mission
        await missionStore.addIRToMission(
            Number(props.mission.id),
            informationRequirementIdsToAdd.value,
        );

        // After successful update, update original values to match current state
        originalInformationRequirementIds.value = [
            ...informationRequirementIdsToAdd.value,
        ];

        // Emit fetch-mission event to refresh mission data in parent and move to next step (3)
        emits('fetch-mission', props.mission.id, 3);
    } catch (error) {
        console.error(
            'Failed to update mission information requirements:',
            error,
        );
    }
};

// Discard changes
const discardChanges = () => {
    if (!props.mission.id) return;

    // Reset to original values
    informationRequirementIdsToAdd.value = [
        ...originalInformationRequirementIds.value,
    ];

    // Update the UI to match original values
    mappedPirs.value.forEach((pir) => {
        if (pir.informationRequirements) {
            pir.informationRequirements.forEach((ir) => {
                ir.isSelected =
                    originalInformationRequirementIds.value.includes(
                        parseInt(ir.id.toString()),
                    );
            });
        }
    });

    // Emit fetch-mission event to move to next step (3)
    emits('fetch-mission', props.mission.id, 3);
};

// Check if there are unsaved changes
const unsavedChanges = computed(() => {
    // If no original values, there are no changes
    if (!originalInformationRequirementIds.value.length) {
        return informationRequirementIdsToAdd.value.length > 0;
    }

    // Different lengths means there are changes
    if (
        originalInformationRequirementIds.value.length !==
        informationRequirementIdsToAdd.value.length
    ) {
        return true;
    }

    // Check if all elements in both arrays match
    const sorted1 = [...originalInformationRequirementIds.value].sort();
    const sorted2 = [...informationRequirementIdsToAdd.value].sort();

    for (let i = 0; i < sorted1.length; i++) {
        if (sorted1[i] !== sorted2[i]) {
            return true;
        }
    }

    return false;
});

// Watch for mission ID changes to refetch PIRs
watch(
    () => props.mission.id,
    async () => {
        await fetchPirsByOperationId();
    },
);

watch(
    mappedPirs,
    () => {
        // Rebuild the array of selected IR IDs
        informationRequirementIdsToAdd.value = [];

        mappedPirs.value.forEach((pir) => {
            if (pir.informationRequirements) {
                pir.informationRequirements.forEach((ir) => {
                    if (ir.isSelected) {
                        informationRequirementIdsToAdd.value.push(
                            parseInt(ir.id.toString()),
                        );
                    }
                });
            }
        });
    },
    { deep: true }, // Deep watching to catch changes in nested objects
);

onMounted(async () => {
    await fetchPirsByOperationId();
});
</script>
