<template>
    <v-dialog
        v-model="isOpen"
        max-width="900"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card class="ma-3 bg-white border border-solid border-grey-lighten-4 rounded-lg overflow-visible">
            <v-card-title class="d-flex justify-space-between">
                <h3>Edit IR</h3>
                <v-btn
                    class="ma-2"
                    variant="flat"
                    icon
                    density="compact"
                    @click="closeIRModal"
                >
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>
            <v-card-text>
                <v-container fluid class="pa-0 text-left">
                    <v-row class="mb-2">
                        <v-col cols="12">
                            <v-textarea
                                density="compact"
                                v-model="IRFields.informationRequirement"
                                label="Information Requirement"
                                rows="2"
                                hide-details
                                variant="outlined"
                                color="primary"
                                base-color="primary"
                                class="bg-white"
                            />
                        </v-col>
                    </v-row>
                    <v-row class="mb-2">
                        <v-col cols="12" md="4">
                            <v-select
                                density="compact"
                                v-model="IRFields.originator"
                                :items="originators"
                                label="Originator"
                                hide-details
                                variant="outlined"
                                color="primary"
                                base-color="primary"
                                class="bg-white"
                            />
                        </v-col>
                        <v-col cols="12" md="4">
                            <v-select
                                density="compact"
                                v-model="IRFields.priority"
                                :items="priorities"
                                label="Priority"
                                hide-details
                                variant="outlined"
                                color="primary"
                                base-color="primary"
                                class="bg-white"
                            />
                        </v-col>
                        <v-col cols="12" md="4">
                            <LumioDateTimePicker
                                v-model="IRFields.ltiovDate as string"
                                class="bg-white"
                                :minDateTime="new Date()"
                                placeholder="LTIoV Date"
                            ></LumioDateTimePicker>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" class="d-flex justify-end pb-6 gap-2">
                            <v-btn
                                @click="closeIRModal"
                                variant="flat"
                                color="secondary"
                                text="Cancel"
                                prepend-icon="mdi-close"
                                class="mr-2"
                                min-width="120"
                            >
                            </v-btn>
                            <v-btn
                                @click="updateIR"
                                variant="flat"
                                color="primary"
                                text="Update IR"
                                prepend-icon="mdi-plus"
                                min-width="120"
                            >
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, watch } from 'vue';
import LumioDateTimePicker from '@/components/elements/LumioDateTimePicker.vue';
import { IOriginator } from '@/types/Originator.type';
import { useAdminOriginatorStore } from '@/stores/admin/originator.store';
import { IPagination } from '@/types/Global.type';
import { IPir } from '@/types/Pir';

const adminOriginatorStore = useAdminOriginatorStore();

const props = defineProps<{
    irId: string | number | null;
    operationId: string | number;
    parentPir: IPir;
}>();

const emit = defineEmits(['close-ir-modal', 'update-ir']);

const isOpen = ref(true);
watch(isOpen, (val) => { if (!val) emit('close-ir-modal'); });

const originators = ref<IOriginator[]>([]);

const priorities = ref<string[]>(['highest', 'high', 'medium', 'low', 'none']);

const IRFields = ref({
    irId: props.irId as string,
    originator: '',
    informationRequirement: '',
    ltiovDate: '',
    priority: 'medium',
});

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    sortBy: 'desc',
    orderBy: 'createdAt',
    pages: 1,
    total: 0,
});

const closeIRModal = () => {
    isOpen.value = false;
};

const updateIR = () => {
    emit('update-ir', IRFields.value);
};

onMounted(async () => {
    const ir = props.parentPir.informationRequirements?.find(
        (ir) => ir.id === props.irId,
    );
    if (ir) {
        IRFields.value = {
            irId: ir.id as string,
            originator: ir.originator,
            informationRequirement: ir.informationRequirement,
            ltiovDate: ir.ltiovDate as unknown as string,
            priority: ir.priority,
        };
    }
    const response = await adminOriginatorStore.fetchOriginators(
        paginationState.value,
        {
            operationId: props.operationId as string,
        },
    );
    originators.value = response.data?.originators ?? [];
});
</script>
