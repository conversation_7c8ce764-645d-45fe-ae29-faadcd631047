import {
	<PERSON><PERSON><PERSON>,
	<PERSON>um<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Index,
	OneToMany
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import OperationModel from "@/models/operation.model.js";
import UserModel from "@/models/user.model.js";
import {Priority, RFIStatus} from "@/interfaces/database.interface.js";
import OriginatorModel from "@/models/originator.model.js";

@Entity("rfis")
export default class RFIModel extends BaseEntity {

	@Column({
		name: "title",
		length: 255,
		type: 'varchar'
	})
	title!: string | null;

	@Column({
		name: "originator_id",
		type: "int"
	})
	originatorId!: number | null;

	@ManyToOne(() => OriginatorModel)
	@JoinColumn({ name: "originator_id" })
	originator!: OriginatorModel;

	@Column({
		name: "originator_label",
		length: 255,
		type: 'varchar',
		nullable: true
	})
	originatorLabel!: string | null;

	@Column({
		name: "ltiov_date",
		type: 'timestamp'
	})
	ltiovDate!: Date;

	@Column({
		name: "priority",
		type: 'enum',
		enum: Priority,
		default: Priority.NONE
	})
	priority!: Priority;

	@Column({
		name: "check_source",
		type: 'boolean',
		default: false
	})
	checkSource!: boolean;

	//justification
	@Column({
		name: "justification",
		type: 'text',
		nullable: true
	})
	justification!: string | null;

	//RFIStatus
	@Column({
		name: "status",
		type: "enum",
		enum: RFIStatus,
		default: RFIStatus.CREATED
	})
	status!: RFIStatus;

	@Column({
		name: "operation_id",
		type: "int"
	})
	operationId!: number | null;

	@ManyToOne(() => OperationModel)
	@JoinColumn({ name: "operation_id" })
	operation!: OperationModel;

	getSearchableFields(): string[] {
		return ['title', 'originatorLabel', 'description'];
	}


}