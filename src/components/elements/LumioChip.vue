<template>
	<v-chip
		:class="[
			fullWidth ? 'w-100' : '',
			backgroundColor || 'bg-primary',
			textColor || 'text-white'
		]"
		density="compact"
		variant="flat"
		size="small"
		label
		:tag="fullWidth ? 'div' : 'span'"
		exact
		:text="text"
		class="font-weight-bold"
	>
	</v-chip>
</template>

<script setup lang="ts">
defineProps({
	text: {
		type: String,
		required: true,
	},
	fullWidth: {
		type: Boolean,
		default: false,
	},
	backgroundColor: {
		type: String,
		default: '',
	},
	textColor: {
		type: String,
		default: '',
	},
});
</script> 