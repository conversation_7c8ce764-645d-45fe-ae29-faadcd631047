<template>
    <v-card class="lumio-card h-screen">
        <v-card-title>
            <h4>Targetable Areas of Interest</h4>
            <div>
                <v-btn
                    v-if="hasPendingChanges"
                    class="mr-2"
                    variant="outlined"
                    color="primary"
                    append-icon="mdi-arrow-right"
                    @click="emits('next-step')"
                >
                    Discard Changes & Next
                </v-btn>
                <v-btn
                    :color="hasPendingChanges ? 'primary' : 'secondary'"
                    append-icon="mdi-arrow-right"
                    @click="storeTais()"
                >
                    {{
                        hasPendingChanges ? 'Save Updates & Next' : 'Next Step'
                    }}
                </v-btn>
            </div>
        </v-card-title>
        <v-card-text class="h-100">
            <v-alert v-if="hasPendingChanges" type="warning" class="mb-4">
                There are changes pending. Please save before continuing.
            </v-alert>

            <div
                v-if="
                    mission &&
                    operation?.locationCoordinates?.coordinates &&
                    aois.length
                "
                class="h-100"
            >
                <v-select
                    multiple
                    chips
                    class=""
                    label="TAIs"
                    :items="mappedAois"
                    v-model="pendingTais"
                    item-title="label"
                    item-value="value"
                    return-object
                    variant="outlined"
                    hide-details
                ></v-select>
                <EsriMapViewer
                    :map-items="parseMapItems"
                    :max-height="600"
                    :operation="operation"
                    :centerCoordinates="getOperationCenter"
                    :zoom="operation.zoom ?? 4"
                    @elementSelected="handleElementClicked"
                ></EsriMapViewer>
            </div>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { Aoi } from '@/types/Aoi.type';

import { IPagination } from '@/types/Global.type';
import { type Mission } from '@/types/Mission';
import { useAdminAoiStore } from '@/stores/admin/aoi.store';
import { useMissionStore } from '@/stores/admin/mission.store';

import type { SymbolItem } from '@/types/EsriMap';
import { parseMapItemsHelper } from '@/composables/misc.helper';

const adminAoiStore = useAdminAoiStore();
const missionStore = useMissionStore();
const aois = ref<Aoi[]>([]);

const props = defineProps<{
    mission?: Mission;
    operation?: any;
}>();

const emits = defineEmits(['fetch-mission', 'next-step']);
const pendingTais = ref<any[]>([]);

const getOperationCenter = computed(() => {
    if (!props.operation) return [0, 0];
    return props.operation.locationCoordinates?.coordinates;
});

// const handlePolygonClicked = (graphic: any) => {
//     const taiId = graphic.attributes.taiId;
//     const tai = mappedAois.value.find((aoi) => aoi.value === taiId);
//
//     if (!tai) return; // Guard clause if TAI not found
//
//     const isExisting = pendingTais.value.some((aoi) => aoi.value === taiId);
//     if (isExisting) {
//         pendingTais.value = pendingTais.value.filter(
//             (aoi) => aoi.value !== taiId,
//         );
//     } else {
//         pendingTais.value.push(tai);
//     }
// };

// watch(
//     // pendingTais,
//     // useDebounceFn(() => {
//     //     if (!existingTais.value) {
//     //         updateTAIs();
//     //     }
//     //
//     //     existingTais.value = false;
//     // }, 1000),
//     // { deep: true },
// );

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 300,
    pages: 1,
    total: 0,
    sortBy: 'createdAt',
    orderBy: 'id',
});

const fetchAois = async () => {
    try {
        const response = await adminAoiStore.fetchAois(
            paginationState.value,
            {
                operationId: props.mission?.operationId?.toString(),
                // isTargetable: 'true',
            },
            // ['operation', 'requestedByUser', 'approvedByUser'],
        );
        const fetchedAois = response.data?.aois;
        if (fetchedAois) {
            aois.value = fetchedAois as Aoi[];
        }
    } catch (error) {
        console.error(error);
    }
};

//parseMapItemsHelper
const parseMapItems = computed((): SymbolItem[] => {
    if (!aois.value) return [];
    return parseMapItemsHelper({
        aois: aois.value,
    });
});

const mappedAois = computed(() => {
    return aois.value.map((aoi) => ({
        label: aoi.name,
        value: aoi.id,
    }));
});

const handleElementClicked = (element: any) => {
    //find aoi by id
    if (element.itemType === 'aoi') {
        const aoi = aois.value.find((aoi) => aoi.id === element.id);
        if (aoi) {
            //check if aoi is already in pending tais
            const existingPendingTai = pendingTais.value.find(
                (tai) => tai.value === aoi.id,
            );
            if (!existingPendingTai) {
                pendingTais.value.push({
                    label: aoi.name,
                    value: aoi.id,
                });
            }
            // emits('fetch-mission', props.mission?.id, 2);
        }
    }
};

const storeTais = async () => {
    await updateTAIs();
    emits('next-step');
};

const updateTAIs = async () => {
    const taiIds = pendingTais.value.map((aoi: any) => Number(aoi.value));
    const missionId = props.mission?.id;
    await missionStore.addTaisToMission(Number(missionId), taiIds);
    emits('next-step');
    // emits('fetch-mission', props.mission?.id, 2);
};

const hasPendingChanges = computed(() => {
    //comnpare mission.tais to pendingtais if there are discrepancy
    const currentTais = props.mission?.tais;
    if (!currentTais) return false;
    //check if currentTais ids match pendingTais ids
    if (currentTais.length !== pendingTais.value.length) return true;

    return currentTais.some((tai, index) => {
        return tai.id !== pendingTais.value[index].value;
    });
});

onMounted(async () => {
    await fetchAois();

    if (props.mission?.tais) {
        pendingTais.value = props.mission.tais.map((tai: any) => ({
            label: tai.name,
            value: tai.id,
            areas: tai.area,
        }));
    }
});
</script>
