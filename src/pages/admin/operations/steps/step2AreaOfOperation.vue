<template>
  <v-card class="lumio-card">
    <v-card-title>
      <h4 class="">Step 2: Area of Operation</h4>
    </v-card-title>
    <v-card-text>
      <v-row>
        <v-col cols="12" md="6">
          <v-text-field
            v-model="opsParams.location"
            variant="outlined"
            density="comfortable"
			label="Location of Operation"
            @update:model-value="sendUpdatedMap"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="6">
          <v-select
            v-model="opsParams.countryCode"
            :items="countryCodes"
            label="Country"
            variant="outlined"
            density="comfortable"
            @update:model-value="sendUpdatedMap"
            searchable
            :filter="filterCountries"
          >
            <template v-slot:item="{ props, item }">
              <v-list-item v-bind="props">
                <template v-slot:prepend>
                  <span :class="`fi fi-${item.value.toLowerCase()}`"></span>
                </template>
              </v-list-item>
            </template>
            <template v-slot:selection="{ item }">
              <span :class="`fi fi-${item.value.toLowerCase()}`" class="mr-2"></span>
              {{ item.title }}
            </template>
          </v-select>
        </v-col>
      </v-row>

		<template v-if="isLoading">
			<v-skeleton-loader
				class="mx-auto"
				type="card"
				width="100%"
				height="500"
			></v-skeleton-loader>
		</template>
		<template v-else>

		<EsriMapSingleArea
			:max-height="500"
			:zoom="zoom"
			:center-coordinates="getCenterCoordinates"
			:map-item="mappedAreaOfOperation"
			@area-deleted="handleAreaDeleted"
			@area-created="handleAreaCreated"
			@area-updated="handleAreaUpdated"
			@area-clicked="handleAreaClicked"
			@zoom-updated="handleZoomUpdated"
			@center-updated="handleCenterUpdated"
			@area-lookedup="handleAreaLookedup"
		></EsriMapSingleArea>
		</template>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import {IArea, IPoint} from '@/types/Global.type';
import { SymbolItem } from '@/types/EsriMap';
import { countryCodes as rawCountryCodes } from '@/utils/esriMap.utils';

const countryCodes = rawCountryCodes.map(country => ({
  title: country.name,
  value: country['alpha-2'].toLowerCase()
}));

const filterCountries = (item: { title: string; value: string }, queryText: string) => {
  const searchText = queryText.toLowerCase();
  return item.title.toLowerCase().includes(searchText) || 
         item.value.toLowerCase().includes(searchText);
};

const isLoading = ref(true);
const props = defineProps<{
    areaOfOperation: IArea;
	locationOfOperation: IPoint;
	zoom: number;
    currentStep: number;
	countryCode: string;
	location: string;
}>();

const opsParams  = ref<{
	areaOfOperation: IArea;
	locationOfOperation: IPoint;
	countryCode: string;
	location: string;
	zoom: number;
}>({
	areaOfOperation: {
		type: 'Polygon',
		coordinates: [],
	},
	locationOfOperation: {
		type: 'Point',
		coordinates: [-98, 38],
	},
	countryCode: '',
	location: '',
	zoom: 0
});

const emits = defineEmits(['update-area-of-ops']);

const getCenterCoordinates = (computed(() => {
	return props.locationOfOperation.coordinates ? props.locationOfOperation.coordinates : [-98, 38]
}));


const mappedAreaOfOperation = computed(() => {
    // Only return a map item if we have valid coordinates
    if (!props.areaOfOperation?.coordinates || 
        !Array.isArray(props.areaOfOperation.coordinates) || 
        props.areaOfOperation.coordinates.length === 0 ||
        props.areaOfOperation.coordinates[0] === null) {
        return null;
    }
    
    return {
        id: null,
        type: 'polygon',
        coordinates: Array.isArray(props.areaOfOperation.coordinates[0]) 
            ? props.areaOfOperation.coordinates[0] 
            : props.areaOfOperation.coordinates
    } as SymbolItem;
});

const handleAreaDeleted = () =>{
	opsParams.value.areaOfOperation.coordinates = [];
	sendUpdatedMap();
}
const handleAreaCreated = (item:any) =>{
	opsParams.value.areaOfOperation = {
		type: 'Polygon',
		coordinates: item.coordinates
	};
	sendUpdatedMap();
}
const handleAreaUpdated = (item:any) =>{
	opsParams.value.areaOfOperation = {
		type: 'Polygon',
		coordinates: item.coordinates
	};
	sendUpdatedMap();
}
const handleAreaClicked = (item:any) =>{
	//
}
const handleZoomUpdated = (zoom:number) =>{
	opsParams.value.zoom = zoom;
	sendUpdatedMap();
}
const handleCenterUpdated = (center:any) =>{
	// Convert from Web Mercator to WGS84
	const x = center.longitude;
	const y = center.latitude;
	const longitude = (x * 180) / 20037508.34;
	const latitude = (Math.atan(Math.exp((y * Math.PI) / 20037508.34)) * 2 - Math.PI / 2) * (180 / Math.PI);

	opsParams.value.locationOfOperation.coordinates = [
		longitude,
		latitude
	];
	sendUpdatedMap();
}

const sendUpdatedMap = () => {
	emits('update-area-of-ops',opsParams.value);
}

const handleAreaLookedup = (countryData:any) =>{
	//TODO: add country data to the area of operation
	opsParams.value.countryCode = countryData.countryCode;
	opsParams.value.location = countryData.country;
	sendUpdatedMap();
}

onMounted(async () => {
	opsParams.value = {
		areaOfOperation: props.areaOfOperation,
		locationOfOperation: props.locationOfOperation,
		zoom: props.zoom,
		countryCode: props.countryCode,
		location: props.location
	}
	isLoading.value = false;
});
</script>
