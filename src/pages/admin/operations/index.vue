<template>
    <div class="ma-4">
        <v-card class="lumio-card">
            <v-card-title>
                <h4>Operations</h4>

                <v-btn
                    v-if="getUserRole === 'org_admin'"
                    color="primary"
                    class="mr-4"
                    to="/admin/operations/builder"
                >
                    <v-icon>mdi-plus</v-icon>
                    Add operation
                </v-btn>
                <span v-else style="color: red; font-size: 12px">
                    You are not authorized to add operations.
                </span>
            </v-card-title>
            <v-card-text>
                <v-progress-linear v-if="loading" indeterminate />
                <v-table
                    v-if="operationsList.length"
                    height="550px"
                    fixed-header
                    density="compact"
                    class="compact-table"
                >
                    <thead>
                        <tr>
                            <th class="">Name</th>
                            <th class="text-center" style="width: 120px">Designation</th>
                            <th class="text-center">Location</th>
                            <th class="text-center" style="width: 100px">Status</th>
                            <th class="text-center" style="width: 200px">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr
                            v-for="operation in operationsList"
                            :key="operation.id"
                        >
                            <td class="text">{{ operation.name }}</td>
                            <td class="text-center">{{ operation.designation }}</td>
                            <td class="text-center">
                                <span class=" align-center">
                                
                                    {{ operation.location }}
                                </span>
                            </td>
                            <td class="text-center">
                                <v-switch
                                class="mx-auto"
                                    v-model="operation.isActive"
                                    :color="operation.isActive ? 'success' : 'grey'"
                                    hide-details
                                    density="compact"
                                    @update:model-value="setOperationState(operation.id, !!operation.isActive)"
                                />
                            </td>
                            <td>
                                <div class="nowrap-col text-center">
                                    <v-btn
                                        v-if="getUserRole === 'org_admin'"
                                        variant="flat"
                                        color="primary"
                                        size="small"
                                        :to="`/admin/operations/${operation.id}/edit`"
                                    >
                                        <v-icon>mdi-pencil</v-icon>
                                    </v-btn>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </v-table>
                <v-alert
                    v-if="operationsList.length === 0 && !loading"
                    type="info"
                    >No operations found. Please add a new operation.</v-alert
                >
                <v-pagination
                    v-model="page"
                    :length="pages"
                    @update:modelValue="getOperations"
                />
            </v-card-text>
        </v-card>
    </div>
</template>

<route lang="yaml">
name: admin-operations-index
meta:
    layout: authenticated # Make sure this is properly indented
    title: Operations
    description: Operations
    protected: true
    admin: true
</route>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Operation } from '@/types/Operation';
import { useOperationStore } from '@/stores/operation.store';
import { useAdminStore } from '@/stores/admin';
import { useAuthStore } from '@/stores/auth';
import { useSnackbar } from '@/composables/useSnackbar';
import { storeToRefs } from 'pinia';

const adminStore = useAdminStore();
const operationStore = useOperationStore();
const { showSnackbar } = useSnackbar();
// import { useDebounceFn } from '@vueuse/core';
const { getUserRole } = storeToRefs(useAuthStore());

const loading = ref(false);
// const search = ref('');
const operationsList = ref<Operation[]>([]);
const page = ref(1);
const perPage = ref(10);
const pages = ref(0);

// const deleteOperation = async (id: string | undefined) => {
//     try {
//         loading.value = true;
//         await adminStore.deleteOperation(id);
//         operationsList.value = operationsList.value.filter(
//             (operation) => operation.id !== id,
//         );
//         await getOperations();
//         showSnackbar({
//             text: 'Operation deleted successfully',
//             color: 'success',
//         });
//     } catch (error) {
//         showSnackbar({
//             text: 'Error deleting operation',
//             color: 'error',
//         });
//     } finally {
//         loading.value = false;
//     }
// };

// const searchOperations = useDebounceFn(async () => {
//     try {
//         loading.value = true;
//         const { data } = search.value
//             ? await adminStore.searchOperations(search.value)
//             : await adminStore.fetchOperations(page.value, perPage.value);
//         operationsList.value = data.operations;
//     } catch (error) {
//         showSnackbar({
//             text: 'Error searching operations',
//             color: 'error',
//         });
//     } finally {
//         loading.value = false;
//     }
// }, 500);

const getOperations = async () => {
    try {
        loading.value = true;
        const { data } = await adminStore.fetchOperations(
            page.value,
            perPage.value,
        );
        const { operations, pagination } = data;
        operationsList.value = operations;
        pages.value = pagination.totalPages;
    } catch (error) {
        showSnackbar({
            text: 'Error fetching operations',
            color: 'error',
        });
        console.error(error);
    } finally {
        loading.value = false;
    }
};

const setOperationState = async (id: string | undefined, isActive: boolean) => {
    if (!id) return;
    try {
        await adminStore.updateOperationStatus(id, isActive);
        await operationStore.refreshCurrentOperation();
        await getOperations();
        showSnackbar({
            text: 'Operation state updated successfully',
            color: 'success',
        });
    } catch (error) {
        console.error(error);
    }
};

onMounted(async () => {
    await getOperations();
});
</script>

<style scoped>
.compact-table .v-data-table__th,
.compact-table .v-data-table__td {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
    font-size: 0.95rem;
}
.compact-table .v-data-table__th {
    font-weight: 600;
}
.compact-table .v-data-table__td {
    vertical-align: middle;
}
.nowrap-col {
    white-space: nowrap !important;
}
</style>
