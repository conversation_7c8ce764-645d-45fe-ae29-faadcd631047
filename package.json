{"name": "lumio-intello-frontend", "version": "1.0.1", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint . --fix --ignore-path .gitignore"}, "dependencies": {"@arcgis/map-components": "^4.30.7", "@mdi/font": "7.4.47", "@types/leaflet": "^1.9.15", "@types/ol": "^6.5.3", "@vuepic/vue-datepicker": "^10.0.0", "@vueuse/core": "^11.1.0", "axios": "1.7.8", "core-js": "^3.37.1", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "esri-loader": "^3.7.0", "flag-icons": "^7.2.3", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "luxon": "^3.5.0", "ol": "^10.5.0", "pinia-plugin-persistedstate": "^4.0.2", "roboto-fontface": "*", "vue": "^3.4.31", "vue-shepherd": "^5.0.1", "vue-timeline-chart": "^2.5.0", "vuetify": "^3.6.11"}, "devDependencies": {"@babel/types": "^7.24.7", "@types/luxon": "^3.4.2", "@types/node": "^20.14.10", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-typescript": "^13.0.0", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-config-vuetify": "^1.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.4.0", "eslint-plugin-vue": "^9.27.0", "pinia": "^2.1.7", "sass": "1.77.6", "typescript": "^5.4.2", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^0.27.2", "unplugin-vue-router": "^0.10.0", "vite": "^5.3.3", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "^2.0.3", "vue-router": "^4.4.0", "vue-tsc": "^2.0.26"}}